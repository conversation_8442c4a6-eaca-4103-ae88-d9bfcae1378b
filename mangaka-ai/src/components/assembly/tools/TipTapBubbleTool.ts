// TipTapBubbleTool - Outil pour créer des bulles TipTap
// Compatible avec le système de coordonnées unifié et le gestionnaire de couches

import { AssemblyElement, DialogueElement, BubbleType } from '../types/assembly.types'
import { generateElementId } from '../managers/StateManager'

interface BubbleCreationState {
  isPlacing: boolean
  x: number
  y: number
  bubbleType: BubbleType
}

export class TipTapBubbleTool {
  private state: BubbleCreationState = {
    isPlacing: false,
    x: 0,
    y: 0,
    bubbleType: 'speech'
  }

  private onBubbleCreated?: (bubble: DialogueElement) => void
  private elements: AssemblyElement[] = []

  constructor(onBubbleCreated?: (bubble: DialogueElement) => void) {
    this.onBubbleCreated = onBubbleCreated
  }

  /**
   * Met à jour la liste des éléments pour la détection de collision
   */
  updateElements(elements: AssemblyElement[]): void {
    this.elements = elements
  }

  /**
   * Getter pour vérifier si l'outil est actif
   */
  get isActive(): boolean {
    return this.state.isPlacing
  }

  /**
   * Change le type de bulle à créer
   */
  setBubbleType(type: BubbleType): void {
    this.state.bubbleType = type
    console.log('🎯 TipTapBubbleTool: Type de bulle défini:', type)
  }

  /**
   * Démarre le mode placement de bulle
   */
  startPlacement(x: number, y: number): void {
    this.state.isPlacing = true
    this.state.x = x
    this.state.y = y

    console.log('🎯 TipTapBubbleTool: Démarrage placement bulle', { 
      type: this.state.bubbleType, 
      position: { x, y } 
    })
  }

  /**
   * Place la bulle à la position actuelle
   */
  placeBubble(x: number, y: number): DialogueElement | null {
    if (!this.state.isPlacing) return null

    // Créer l'élément bulle TipTap
    const bubble: DialogueElement = {
      id: generateElementId(),
      type: 'dialogue',
      layerType: 'dialogue',
      text: '', // Texte vide pour édition immédiate
      transform: {
        x,
        y,
        rotation: 0,
        alpha: 1,
        zIndex: 200, // Au-dessus des panels
        width: 150,  // Taille par défaut
        height: 80
      },
      dialogueStyle: {
        type: this.state.bubbleType,
        backgroundColor: this.getBubbleStyleDefaults(this.state.bubbleType).backgroundColor,
        outlineColor: this.getBubbleStyleDefaults(this.state.bubbleType).outlineColor,
        outlineWidth: this.getBubbleStyleDefaults(this.state.bubbleType).outlineWidth,
        textColor: 0x000000,
        fontSize: 16,
        fontFamily: 'Arial, sans-serif',
        textAlign: 'center',
        dashedOutline: this.state.bubbleType === 'whisper' || this.state.bubbleType === 'thought',
        tailPosition: 'bottom-left',
        tailLength: 30,
        tailAngleDegrees: 225,
        tailAttachmentSide: 'bottom'
      },
      properties: {
        visible: true,
        locked: false,
        selectable: true,
        name: `Bulle ${this.state.bubbleType}`
      }
    }

    // Vérifier les collisions et ajuster si nécessaire
    if (this.checkCollision(bubble)) {
      console.log('⚠️ TipTapBubbleTool: Collision détectée, ajustement automatique')
      bubble.transform.x += 10
      bubble.transform.y += 10
    }

    // Terminer le placement
    this.state.isPlacing = false

    // Notifier la création
    if (this.onBubbleCreated) {
      this.onBubbleCreated(bubble)
    }

    console.log('✅ TipTapBubbleTool: Bulle créée:', bubble)
    return bubble
  }

  /**
   * Annule le placement en cours
   */
  cancelPlacement(): void {
    this.state.isPlacing = false
    console.log('❌ TipTapBubbleTool: Placement annulé')
  }

  /**
   * Vérifie les collisions avec les éléments existants
   */
  private checkCollision(bubble: DialogueElement): boolean {
    const bubbleBounds = {
      x: bubble.transform.x,
      y: bubble.transform.y,
      width: bubble.transform.width,
      height: bubble.transform.height
    }

    return this.elements.some(element => {
      if (element.id === bubble.id) return false

      const elementBounds = {
        x: element.transform.x,
        y: element.transform.y,
        width: element.transform.width,
        height: element.transform.height
      }

      return this.rectanglesOverlap(bubbleBounds, elementBounds)
    })
  }

  /**
   * Vérifie si deux rectangles se chevauchent
   */
  private rectanglesOverlap(rect1: any, rect2: any): boolean {
    return !(rect1.x + rect1.width < rect2.x || 
             rect2.x + rect2.width < rect1.x || 
             rect1.y + rect1.height < rect2.y || 
             rect2.y + rect2.height < rect1.y)
  }

  /**
   * Retourne les styles par défaut selon le type de bulle
   */
  private getBubbleStyleDefaults(type: BubbleType) {
    const defaults = {
      speech: {
        backgroundColor: 0xffffff,
        outlineColor: 0x000000,
        outlineWidth: 2
      },
      thought: {
        backgroundColor: 0xf0f8ff,
        outlineColor: 0x4169e1,
        outlineWidth: 1
      },
      shout: {
        backgroundColor: 0xfff5ee,
        outlineColor: 0xff4500,
        outlineWidth: 3
      },
      whisper: {
        backgroundColor: 0xf5f5f5,
        outlineColor: 0x696969,
        outlineWidth: 1
      },
      explosion: {
        backgroundColor: 0xffffe0,
        outlineColor: 0xffd700,
        outlineWidth: 4
      }
    }

    return defaults[type] || defaults.speech
  }

  /**
   * Nettoie les ressources
   */
  destroy(): void {
    this.state.isPlacing = false
    this.onBubbleCreated = undefined
    this.elements = []
  }
}

/**
 * Hook pour utiliser TipTapBubbleTool
 */
export function useTipTapBubbleTool(onBubbleCreated?: (bubble: DialogueElement) => void) {
  const tool = new TipTapBubbleTool(onBubbleCreated)

  return {
    setBubbleType: tool.setBubbleType.bind(tool),
    startPlacement: tool.startPlacement.bind(tool),
    placeBubble: tool.placeBubble.bind(tool),
    cancelPlacement: tool.cancelPlacement.bind(tool),
    updateElements: tool.updateElements.bind(tool),
    isActive: tool.isActive,
    destroy: tool.destroy.bind(tool)
  }
}
