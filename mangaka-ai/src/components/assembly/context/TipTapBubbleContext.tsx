'use client'

// TipTapBubbleContext - Contexte pour gérer les bulles TipTap
// Système d'état centralisé pour les nouvelles bulles

import React, { createContext, useContext, useState, useCallback, useMemo } from 'react'
import { DialogueElement, BubbleType } from '../types/assembly.types'
import { generateElementId } from '../managers/StateManager'
import { BubbleMode } from '../ui/TipTapBubble'

interface TipTapBubbleContextType {
  // État
  bubbles: DialogueElement[]
  selectedBubbleIds: string[]
  bubbleModes: Record<string, BubbleMode>
  
  // Actions
  createBubble: (x: number, y: number, bubbleType: BubbleType) => DialogueElement
  updateBubble: (bubbleId: string, updates: Partial<DialogueElement>) => void
  deleteBubble: (bubbleId: string) => void
  selectBubble: (bubbleId: string) => void
  deselectBubble: (bubbleId: string) => void
  clearSelection: () => void
  setBubbleMode: (bubbleId: string, mode: BubbleMode) => void
  getBubbleMode: (bubbleId: string) => BubbleMode
}

const TipTapBubbleContext = createContext<TipTapBubbleContextType | null>(null)

interface TipTapBubbleProviderProps {
  children: React.ReactNode
}

/**
 * Provider pour le contexte des bulles TipTap
 * Gère l'état centralisé des bulles et leurs modes
 */
export function TipTapBubbleProvider({ children }: TipTapBubbleProviderProps) {
  const [bubbles, setBubbles] = useState<DialogueElement[]>([])
  const [selectedBubbleIds, setSelectedBubbleIds] = useState<string[]>([])
  const [bubbleModes, setBubbleModes] = useState<Record<string, BubbleMode>>({})

  // ✅ CRÉATION DE BULLE
  const createBubble = useCallback((x: number, y: number, bubbleType: BubbleType): DialogueElement => {
    const newBubble: DialogueElement = {
      id: generateElementId(),
      type: 'dialogue',
      layerType: 'dialogue',
      text: '', // Texte vide pour édition immédiate
      transform: {
        x,
        y,
        rotation: 0,
        alpha: 1,
        zIndex: 200,
        width: 150,
        height: 80
      },
      dialogueStyle: {
        type: bubbleType,
        backgroundColor: getBubbleStyleDefaults(bubbleType).backgroundColor,
        outlineColor: getBubbleStyleDefaults(bubbleType).outlineColor,
        outlineWidth: getBubbleStyleDefaults(bubbleType).outlineWidth,
        textColor: 0x000000,
        fontSize: 16,
        fontFamily: 'Arial, sans-serif',
        textAlign: 'center',
        dashedOutline: bubbleType === 'whisper' || bubbleType === 'thought',
        tailPosition: 'bottom-left',
        tailLength: 30,
        tailAngleDegrees: 225,
        tailAttachmentSide: 'bottom'
      },
      properties: {
        visible: true,
        locked: false,
        selectable: true,
        name: `Bulle ${bubbleType}`
      }
    }

    setBubbles(prev => [...prev, newBubble])
    setSelectedBubbleIds([newBubble.id])
    setBubbleModes(prev => ({ ...prev, [newBubble.id]: 'editing' })) // Commencer en mode édition

    console.log('✅ TipTapBubbleContext: Bulle créée:', newBubble)
    return newBubble
  }, [])

  // ✅ MISE À JOUR DE BULLE
  const updateBubble = useCallback((bubbleId: string, updates: Partial<DialogueElement>) => {
    setBubbles(prev => prev.map(bubble => 
      bubble.id === bubbleId ? { ...bubble, ...updates } : bubble
    ))
    console.log('🔄 TipTapBubbleContext: Bulle mise à jour:', bubbleId, updates)
  }, [])

  // ✅ SUPPRESSION DE BULLE
  const deleteBubble = useCallback((bubbleId: string) => {
    setBubbles(prev => prev.filter(bubble => bubble.id !== bubbleId))
    setSelectedBubbleIds(prev => prev.filter(id => id !== bubbleId))
    setBubbleModes(prev => {
      const newModes = { ...prev }
      delete newModes[bubbleId]
      return newModes
    })
    console.log('❌ TipTapBubbleContext: Bulle supprimée:', bubbleId)
  }, [])

  // ✅ SÉLECTION
  const selectBubble = useCallback((bubbleId: string) => {
    setSelectedBubbleIds([bubbleId])
    console.log('🎯 TipTapBubbleContext: Bulle sélectionnée:', bubbleId)
  }, [])

  const deselectBubble = useCallback((bubbleId: string) => {
    setSelectedBubbleIds(prev => prev.filter(id => id !== bubbleId))
  }, [])

  const clearSelection = useCallback(() => {
    setSelectedBubbleIds([])
  }, [])

  // ✅ GESTION DES MODES
  const setBubbleMode = useCallback((bubbleId: string, mode: BubbleMode) => {
    setBubbleModes(prev => ({ ...prev, [bubbleId]: mode }))
    console.log('🔄 TipTapBubbleContext: Mode bulle changé:', bubbleId, mode)
  }, [])

  const getBubbleMode = useCallback((bubbleId: string): BubbleMode => {
    return bubbleModes[bubbleId] || 'reading'
  }, [bubbleModes])

  // ✅ VALEUR DU CONTEXTE
  const contextValue = useMemo<TipTapBubbleContextType>(() => ({
    bubbles,
    selectedBubbleIds,
    bubbleModes,
    createBubble,
    updateBubble,
    deleteBubble,
    selectBubble,
    deselectBubble,
    clearSelection,
    setBubbleMode,
    getBubbleMode
  }), [
    bubbles,
    selectedBubbleIds,
    bubbleModes,
    createBubble,
    updateBubble,
    deleteBubble,
    selectBubble,
    deselectBubble,
    clearSelection,
    setBubbleMode,
    getBubbleMode
  ])

  return (
    <TipTapBubbleContext.Provider value={contextValue}>
      {children}
    </TipTapBubbleContext.Provider>
  )
}

/**
 * Hook pour utiliser le contexte des bulles TipTap
 */
export function useTipTapBubbleContext() {
  const context = useContext(TipTapBubbleContext)
  if (!context) {
    throw new Error('useTipTapBubbleContext must be used within a TipTapBubbleProvider')
  }
  return context
}

// ✅ STYLES PAR DÉFAUT SELON LE TYPE
function getBubbleStyleDefaults(type: BubbleType) {
  const defaults = {
    speech: {
      backgroundColor: 0xffffff,
      outlineColor: 0x000000,
      outlineWidth: 2
    },
    thought: {
      backgroundColor: 0xf0f8ff,
      outlineColor: 0x4169e1,
      outlineWidth: 1
    },
    shout: {
      backgroundColor: 0xfff5ee,
      outlineColor: 0xff4500,
      outlineWidth: 3
    },
    whisper: {
      backgroundColor: 0xf5f5f5,
      outlineColor: 0x696969,
      outlineWidth: 1
    },
    explosion: {
      backgroundColor: 0xffffe0,
      outlineColor: 0xffd700,
      outlineWidth: 4
    }
  }

  return defaults[type] || defaults.speech
}
