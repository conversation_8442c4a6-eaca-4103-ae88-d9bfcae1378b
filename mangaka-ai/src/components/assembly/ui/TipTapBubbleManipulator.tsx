'use client'

// TipTapBubbleManipulator - Système de sélection et manipulation IDENTIQUE aux panels
// Réplique EXACTEMENT le comportement de KonvaPanel pour les speech bubbles

import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react'
import { DialogueElement } from '../types/assembly.types'
import { BubbleMode } from './TipTapBubble'

interface TipTapBubbleManipulatorProps {
  element: DialogueElement
  isSelected: boolean
  mode: BubbleMode
  onUpdate: (elementId: string, updates: Partial<DialogueElement>) => void
  onModeChange: (bubbleId: string, mode: BubbleMode) => void
  onDoubleClick?: (bubbleId: string) => void
  children: React.ReactNode
}

// ✅ RÉPLICATION EXACTE : Types de handles identiques à KonvaPanel
enum HandleType {
  CORNER_NW = 0,
  EDGE_N = 1,
  CORNER_NE = 2,
  EDGE_E = 3,
  CORNER_SE = 4,
  EDGE_S = 5,
  CORNER_SW = 6,
  EDGE_W = 7
}

// ✅ RÉPLICATION EXACTE : Curseurs identiques à KonvaPanel
const HANDLE_CURSORS = [
  'nw-resize', 'n-resize', 'ne-resize', 'e-resize',
  'se-resize', 's-resize', 'sw-resize', 'w-resize'
]

/**
 * Composant de manipulation pour les bulles TipTap
 * Réplique EXACTEMENT le système de sélection/manipulation des panels
 */
export default function TipTapBubbleManipulator({
  element,
  isSelected,
  mode,
  onUpdate,
  onModeChange,
  onDoubleClick,
  children
}: TipTapBubbleManipulatorProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  
  // ✅ RÉPLICATION EXACTE : États identiques à KonvaPanel
  const [isDragging, setIsDragging] = useState(false)
  const [isResizing, setIsResizing] = useState(false)
  const [activeHandle, setActiveHandle] = useState<number | null>(null)
  const [dragStartPos, setDragStartPos] = useState({ x: 0, y: 0 })
  const [elementStartPos, setElementStartPos] = useState({ x: 0, y: 0 })
  const [elementStartSize, setElementStartSize] = useState({ width: 0, height: 0 })

  // ✅ RÉPLICATION EXACTE : Positions des handles identiques à KonvaPanel
  const handlePositions = useMemo(() => {
    const { width, height } = element.transform
    return [
      { x: 0, y: 0 },                    // CORNER_NW
      { x: width / 2, y: 0 },            // EDGE_N
      { x: width, y: 0 },                // CORNER_NE
      { x: width, y: height / 2 },       // EDGE_E
      { x: width, y: height },           // CORNER_SE
      { x: width / 2, y: height },       // EDGE_S
      { x: 0, y: height },               // CORNER_SW
      { x: 0, y: height / 2 }            // EDGE_W
    ]
  }, [element.transform.width, element.transform.height])

  // ✅ RÉPLICATION EXACTE : Gestionnaire de clic identique à KonvaPanel
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()
    
    if (mode === 'reading') {
      console.log('🎯 TipTapBubble cliqué (mode reading):', element.id)
      onModeChange(element.id, 'manipulating')
    }
  }, [element.id, mode, onModeChange])

  // ✅ RÉPLICATION EXACTE : Gestionnaire de double-clic pour édition
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()

    console.log('🎯 TipTapBubble double-cliqué:', element.id)
    if (onDoubleClick) {
      onDoubleClick(element.id)
    }
  }, [element.id, onDoubleClick])

  // ✅ RÉPLICATION EXACTE : Gestionnaire de drag identique à KonvaPanel
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (mode !== 'manipulating' || isResizing) return

    e.preventDefault()
    setIsDragging(true)
    setDragStartPos({ x: e.clientX, y: e.clientY })
    setElementStartPos({ x: element.transform.x, y: element.transform.y })

    console.log('🎯 Bubble drag start:', element.id)
  }, [element.transform, mode, isResizing])

  // ✅ RÉPLICATION EXACTE : Gestionnaire de handles identique à KonvaPanel
  const handleHandleMouseDown = useCallback((handleIndex: number, e: React.MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()

    setIsResizing(true)
    setActiveHandle(handleIndex)
    setDragStartPos({ x: e.clientX, y: e.clientY })
    setElementStartPos({ x: element.transform.x, y: element.transform.y })
    setElementStartSize({ width: element.transform.width, height: element.transform.height })

    console.log('🔧 Handle resize start:', HandleType[handleIndex], 'pour bubble:', element.id)
  }, [element.transform])

  // ✅ RÉPLICATION EXACTE : Gestionnaire de resize identique à KonvaPanel
  const handleResize = useCallback((newWidth: number, newHeight: number, newX?: number, newY?: number) => {
    onUpdate(element.id, {
      transform: {
        ...element.transform,
        x: newX !== undefined ? newX : element.transform.x,
        y: newY !== undefined ? newY : element.transform.y,
        width: Math.max(50, newWidth), // Taille minimale pour les bulles
        height: Math.max(30, newHeight)
      }
    })
  }, [element.id, element.transform, onUpdate])

  // ✅ RÉPLICATION EXACTE : Gestionnaires globaux identiques à KonvaPanel
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging && mode === 'manipulating') {
        const deltaX = e.clientX - dragStartPos.x
        const deltaY = e.clientY - dragStartPos.y
        
        onUpdate(element.id, {
          transform: {
            ...element.transform,
            x: elementStartPos.x + deltaX,
            y: elementStartPos.y + deltaY
          }
        })
      }
      
      if (isResizing && activeHandle !== null) {
        const deltaX = e.clientX - dragStartPos.x
        const deltaY = e.clientY - dragStartPos.y
        
        let newWidth = elementStartSize.width
        let newHeight = elementStartSize.height
        let newX = elementStartPos.x
        let newY = elementStartPos.y

        // ✅ RÉPLICATION EXACTE : Logique de resize identique à KonvaPanel
        switch (activeHandle) {
          case HandleType.CORNER_NW:
            newWidth = elementStartSize.width - deltaX
            newHeight = elementStartSize.height - deltaY
            newX = elementStartPos.x + deltaX
            newY = elementStartPos.y + deltaY
            break
          case HandleType.EDGE_N:
            newHeight = elementStartSize.height - deltaY
            newY = elementStartPos.y + deltaY
            break
          case HandleType.CORNER_NE:
            newWidth = elementStartSize.width + deltaX
            newHeight = elementStartSize.height - deltaY
            newY = elementStartPos.y + deltaY
            break
          case HandleType.EDGE_E:
            newWidth = elementStartSize.width + deltaX
            break
          case HandleType.CORNER_SE:
            newWidth = elementStartSize.width + deltaX
            newHeight = elementStartSize.height + deltaY
            break
          case HandleType.EDGE_S:
            newHeight = elementStartSize.height + deltaY
            break
          case HandleType.CORNER_SW:
            newWidth = elementStartSize.width - deltaX
            newHeight = elementStartSize.height + deltaY
            newX = elementStartPos.x + deltaX
            break
          case HandleType.EDGE_W:
            newWidth = elementStartSize.width - deltaX
            newX = elementStartPos.x + deltaX
            break
        }

        handleResize(newWidth, newHeight, newX, newY)
      }
    }

    const handleMouseUp = () => {
      if (isDragging) {
        setIsDragging(false)
        console.log('🎯 Bubble drag end:', element.id)
      }
      
      if (isResizing) {
        setIsResizing(false)
        setActiveHandle(null)
        console.log('🔧 Bubble resize end:', element.id)
      }
    }

    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, isResizing, activeHandle, dragStartPos, elementStartPos, elementStartSize, element.id, mode, onUpdate, handleResize])

  // ✅ STYLES DE CONTENEUR
  const containerStyle = useMemo(() => ({
    position: 'absolute' as const,
    left: `${element.transform.x}px`,
    top: `${element.transform.y}px`,
    width: `${element.transform.width}px`,
    height: `${element.transform.height}px`,
    cursor: mode === 'manipulating' && !isResizing ? 'move' : 'default',
    pointerEvents: 'auto' as const,
    zIndex: element.transform.zIndex
  }), [element.transform, mode, isResizing])

  return (
    <div
      ref={containerRef}
      style={containerStyle}
      onMouseDown={handleMouseDown}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      className="tiptap-bubble-manipulator"
    >
      {/* Contenu de la bulle (TipTap) */}
      {children}

      {/* ✅ RÉPLICATION EXACTE : Bordure de sélection identique à KonvaPanel */}
      {isSelected && mode === 'manipulating' && (
        <div
          className="absolute inset-0 border-2 border-blue-500 pointer-events-none"
          style={{
            borderStyle: 'solid',
            borderColor: '#3b82f6'
          }}
        />
      )}

      {/* ✅ RÉPLICATION EXACTE : Handles de redimensionnement identiques à KonvaPanel */}
      {isSelected && mode === 'manipulating' && !element.properties.locked && handlePositions.map((pos, index) => (
        <div
          key={`handle-${index}`}
          className="absolute w-2 h-2 bg-blue-500 border border-white rounded-full cursor-pointer"
          style={{
            left: `${pos.x - 4}px`,
            top: `${pos.y - 4}px`,
            cursor: HANDLE_CURSORS[index],
            zIndex: 1000
          }}
          onMouseDown={(e) => handleHandleMouseDown(index, e)}
        />
      ))}

      {/* ✅ INDICATEUR DE MODE ÉDITION */}
      {mode === 'editing' && (
        <div
          className="absolute inset-0 border-2 border-green-500 pointer-events-none"
          style={{
            borderStyle: 'dashed',
            borderColor: '#10b981'
          }}
        />
      )}
    </div>
  )
}
