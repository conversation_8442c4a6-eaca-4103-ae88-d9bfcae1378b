'use client'

// EnhancedBubbleQueue - Advanced directional queue system for speech bubbles
// Features: 360° rotation, variable length, interactive manipulation, seamless visual integration

import React, { useMemo, useCallback } from 'react'
import { DialogueElement, QueueConfiguration } from '../types/assembly.types'

interface EnhancedBubbleQueueProps {
  element: DialogueElement
  className?: string
  onQueueUpdate?: (queueConfig: QueueConfiguration) => void
  isManipulating?: boolean
}

/**
 * Enhanced queue component with 360° directional capability and interactive manipulation
 */
export default function EnhancedBubbleQueue({ 
  element, 
  className = '',
  onQueueUpdate,
  isManipulating = false
}: EnhancedBubbleQueueProps) {
  
  const queueConfig = element.dialogueStyle.queue
  const { width, height } = element.transform

  // ✅ CALCULATE QUEUE ATTACHMENT POINT ON BUBBLE PERIMETER
  const attachmentPoint = useMemo(() => {
    const centerX = width / 2
    const centerY = height / 2
    const radiusX = width / 2
    const radiusY = height / 2
    
    // Convert angle to radians
    const angleRad = (queueConfig.angle * Math.PI) / 180
    
    // Calculate point on ellipse perimeter
    const x = centerX + Math.cos(angleRad) * radiusX * 0.9 // 0.9 for slight inset
    const y = centerY + Math.sin(angleRad) * radiusY * 0.9
    
    return { x, y }
  }, [queueConfig.angle, width, height])

  // ✅ CALCULATE QUEUE TIP POSITION
  const tipPosition = useMemo(() => {
    const angleRad = (queueConfig.angle * Math.PI) / 180
    const x = attachmentPoint.x + Math.cos(angleRad) * queueConfig.length
    const y = attachmentPoint.y + Math.sin(angleRad) * queueConfig.length
    
    return { x, y }
  }, [attachmentPoint, queueConfig.angle, queueConfig.length])

  // ✅ GENERATE SVG PATH BASED ON QUEUE STYLE
  const queuePath = useMemo(() => {
    const { style, thickness, curvature = 0, tapering = 0 } = queueConfig
    const { x: startX, y: startY } = attachmentPoint
    const { x: endX, y: endY } = tipPosition
    
    switch (style) {
      case 'triangle':
        return generateTriangleQueue(startX, startY, endX, endY, thickness, tapering)
      case 'curved':
        return generateCurvedQueue(startX, startY, endX, endY, thickness, curvature)
      case 'jagged':
        return generateJaggedQueue(startX, startY, endX, endY, thickness)
      case 'thin':
        return generateThinQueue(startX, startY, endX, endY, thickness)
      default:
        return generateTriangleQueue(startX, startY, endX, endY, thickness, tapering)
    }
  }, [attachmentPoint, tipPosition, queueConfig])

  // ✅ QUEUE STYLING BASED ON BUBBLE STYLE
  const queueStyle = useMemo(() => {
    const { backgroundColor, outlineColor, outlineWidth, type } = element.dialogueStyle
    
    const baseStyle = {
      fill: typeof backgroundColor === 'string' 
        ? backgroundColor 
        : `#${backgroundColor.toString(16).padStart(6, '0')}`,
      stroke: typeof outlineColor === 'string' 
        ? outlineColor 
        : `#${outlineColor.toString(16).padStart(6, '0')}`,
      strokeWidth: outlineWidth
    }

    // Type-specific styling
    switch (type) {
      case 'thought':
        return { ...baseStyle, strokeDasharray: '5,5' }
      case 'whisper':
        return { ...baseStyle, strokeDasharray: '3,3', opacity: 0.8 }
      case 'shout':
        return { ...baseStyle, strokeWidth: outlineWidth * 1.5 }
      default:
        return baseStyle
    }
  }, [element.dialogueStyle])

  // Don't render queue for explosion type
  if (element.dialogueStyle.type === 'explosion') {
    return null
  }

  return (
    <svg
      className={`enhanced-bubble-queue absolute ${className}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        overflow: 'visible',
        zIndex: 1 // Below bubble content but above background
      }}
    >
      {/* Main queue path */}
      <path
        d={queuePath}
        style={queueStyle}
      />
      
      {/* Seamless connection to bubble border if enabled */}
      {queueConfig.seamlessConnection && (
        <circle
          cx={attachmentPoint.x}
          cy={attachmentPoint.y}
          r={queueConfig.thickness / 2}
          style={{
            fill: queueStyle.fill,
            stroke: 'none'
          }}
        />
      )}
    </svg>
  )
}

// ✅ QUEUE GENERATION FUNCTIONS

/**
 * Generate triangular queue path (classic speech bubble)
 */
function generateTriangleQueue(
  startX: number, 
  startY: number, 
  endX: number, 
  endY: number, 
  thickness: number,
  tapering: number = 0
): string {
  const angle = Math.atan2(endY - startY, endX - startX)
  const perpAngle = angle + Math.PI / 2
  
  // Calculate base width (with optional tapering)
  const baseWidth = thickness * (1 - tapering * 0.5)
  const tipWidth = thickness * (1 - tapering)
  
  // Base points
  const base1X = startX + Math.cos(perpAngle) * (baseWidth / 2)
  const base1Y = startY + Math.sin(perpAngle) * (baseWidth / 2)
  const base2X = startX - Math.cos(perpAngle) * (baseWidth / 2)
  const base2Y = startY - Math.sin(perpAngle) * (baseWidth / 2)
  
  // Tip points (for tapering effect)
  const tip1X = endX + Math.cos(perpAngle) * (tipWidth / 2)
  const tip1Y = endY + Math.sin(perpAngle) * (tipWidth / 2)
  const tip2X = endX - Math.cos(perpAngle) * (tipWidth / 2)
  const tip2Y = endY - Math.sin(perpAngle) * (tipWidth / 2)
  
  if (tapering > 0) {
    return `M ${base1X} ${base1Y} L ${tip1X} ${tip1Y} L ${endX} ${endY} L ${tip2X} ${tip2Y} L ${base2X} ${base2Y} Z`
  } else {
    return `M ${base1X} ${base1Y} L ${endX} ${endY} L ${base2X} ${base2Y} Z`
  }
}

/**
 * Generate curved queue path
 */
function generateCurvedQueue(
  startX: number, 
  startY: number, 
  endX: number, 
  endY: number, 
  thickness: number,
  curvature: number = 0.3
): string {
  const angle = Math.atan2(endY - startY, endX - startX)
  const perpAngle = angle + Math.PI / 2
  const distance = Math.sqrt((endX - startX) ** 2 + (endY - startY) ** 2)
  
  // Control point for curve
  const controlX = (startX + endX) / 2 + Math.cos(perpAngle) * distance * curvature
  const controlY = (startY + endY) / 2 + Math.sin(perpAngle) * distance * curvature
  
  // Base points
  const base1X = startX + Math.cos(perpAngle) * (thickness / 2)
  const base1Y = startY + Math.sin(perpAngle) * (thickness / 2)
  const base2X = startX - Math.cos(perpAngle) * (thickness / 2)
  const base2Y = startY - Math.sin(perpAngle) * (thickness / 2)
  
  return `M ${base1X} ${base1Y} Q ${controlX} ${controlY} ${endX} ${endY} Q ${controlX - 10} ${controlY} ${base2X} ${base2Y} Z`
}

/**
 * Generate jagged queue path (for shout bubbles)
 */
function generateJaggedQueue(
  startX: number, 
  startY: number, 
  endX: number, 
  endY: number, 
  thickness: number
): string {
  const distance = Math.sqrt((endX - startX) ** 2 + (endY - startY) ** 2)
  const angle = Math.atan2(endY - startY, endX - startX)
  const perpAngle = angle + Math.PI / 2
  
  const points = []
  const numSpikes = Math.max(3, Math.floor(distance / 15))
  
  for (let i = 0; i <= numSpikes; i++) {
    const progress = i / numSpikes
    const currentX = startX + (endX - startX) * progress
    const currentY = startY + (endY - startY) * progress
    
    // Alternate spike direction
    const spikeOffset = (i % 2 === 0) ? 0 : thickness * 0.3
    const pointX = currentX + Math.cos(perpAngle) * spikeOffset
    const pointY = currentY + Math.sin(perpAngle) * spikeOffset
    
    points.push(`${i === 0 ? 'M' : 'L'} ${pointX} ${pointY}`)
  }
  
  return points.join(' ')
}

/**
 * Generate thin queue path (for whisper bubbles)
 */
function generateThinQueue(
  startX: number, 
  startY: number, 
  endX: number, 
  endY: number, 
  thickness: number
): string {
  const thinThickness = thickness * 0.5
  const angle = Math.atan2(endY - startY, endX - startX)
  const perpAngle = angle + Math.PI / 2
  
  const base1X = startX + Math.cos(perpAngle) * (thinThickness / 2)
  const base1Y = startY + Math.sin(perpAngle) * (thinThickness / 2)
  const base2X = startX - Math.cos(perpAngle) * (thinThickness / 2)
  const base2Y = startY - Math.sin(perpAngle) * (thinThickness / 2)
  
  return `M ${base1X} ${base1Y} L ${endX} ${endY} L ${base2X} ${base2Y} Z`
}
