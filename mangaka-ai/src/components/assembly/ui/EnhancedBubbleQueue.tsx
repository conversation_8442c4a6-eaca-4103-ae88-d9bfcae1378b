'use client'

// EnhancedBubbleQueue - Advanced directional queue system for speech bubbles
// Features: 360° rotation, variable length, interactive manipulation, seamless visual integration

import React, { useMemo, useCallback } from 'react'
import { DialogueElement, QueueConfiguration } from '../types/assembly.types'

interface EnhancedBubbleQueueProps {
  element: DialogueElement
  className?: string
  onQueueUpdate?: (queueConfig: QueueConfiguration) => void
  isManipulating?: boolean
}

/**
 * Enhanced queue component with 360° directional capability and interactive manipulation
 */
export default function EnhancedBubbleQueue({
  element,
  className = '',
  onQueueUpdate,
  isManipulating = false
}: EnhancedBubbleQueueProps) {

  // ✅ MIGRATION AUTOMATIQUE : Créer queue config si manquant
  const queueConfig = useMemo(() => {
    if (element.dialogueStyle.queue) {
      return element.dialogueStyle.queue
    }

    // Migration depuis legacy properties
    console.log('🔄 Migrating legacy queue properties for:', element.id)
    return {
      angle: element.dialogueStyle.tailAngleDegrees || 225,
      length: element.dialogueStyle.tailLength || 50,
      thickness: 24,
      style: 'triangle' as const,
      seamlessConnection: true,
      isManipulating: false,
      showHandles: false,
      snapToCardinal: false,
      curvature: 0.3,
      tapering: 0.85
    }
  }, [element.dialogueStyle])

  const { width, height } = element.transform

  // ✅ CALCULATE QUEUE ATTACHMENT POINT ON BUBBLE PERIMETER (INTELLIGENT)
  const attachmentPoint = useMemo(() => {
    const centerX = width / 2
    const centerY = height / 2
    const angleRad = (queueConfig.angle * Math.PI) / 180

    // ✅ CALCUL INTELLIGENT : Point exact sur le bord de la bulle
    // Intersection entre direction de la queue et ellipse de la bulle
    const radiusX = width / 2
    const radiusY = height / 2

    // Calcul précis du point d'intersection sur l'ellipse
    const cos = Math.cos(angleRad)
    const sin = Math.sin(angleRad)
    const denominator = Math.sqrt((radiusY * cos) ** 2 + (radiusX * sin) ** 2)

    const x = centerX + (radiusX * radiusY * cos) / denominator
    const y = centerY + (radiusX * radiusY * sin) / denominator

    return { x, y }
  }, [queueConfig.angle, width, height])

  // ✅ CALCULATE QUEUE TIP POSITION
  const tipPosition = useMemo(() => {
    const angleRad = (queueConfig.angle * Math.PI) / 180
    const x = attachmentPoint.x + Math.cos(angleRad) * queueConfig.length
    const y = attachmentPoint.y + Math.sin(angleRad) * queueConfig.length
    
    return { x, y }
  }, [attachmentPoint, queueConfig.angle, queueConfig.length])

  // ✅ GENERATE SVG PATH BASED ON QUEUE STYLE
  const queuePath = useMemo(() => {
    const { style, thickness, curvature = 0, tapering = 0 } = queueConfig
    const { x: startX, y: startY } = attachmentPoint
    const { x: endX, y: endY } = tipPosition
    
    switch (style) {
      case 'triangle':
        return generateTriangleQueue(startX, startY, endX, endY, thickness, tapering)
      case 'curved':
        return generateCurvedQueue(startX, startY, endX, endY, thickness, curvature)
      case 'jagged':
        return generateJaggedQueue(startX, startY, endX, endY, thickness)
      case 'thin':
        return generateThinQueue(startX, startY, endX, endY, thickness)
      default:
        return generateTriangleQueue(startX, startY, endX, endY, thickness, tapering)
    }
  }, [attachmentPoint, tipPosition, queueConfig])

  // ✅ QUEUE STYLING BASED ON BUBBLE STYLE
  const queueStyle = useMemo(() => {
    const { backgroundColor, outlineColor, outlineWidth, type } = element.dialogueStyle
    
    const baseStyle = {
      fill: typeof backgroundColor === 'string' 
        ? backgroundColor 
        : `#${backgroundColor.toString(16).padStart(6, '0')}`,
      stroke: typeof outlineColor === 'string' 
        ? outlineColor 
        : `#${outlineColor.toString(16).padStart(6, '0')}`,
      strokeWidth: outlineWidth
    }

    // Type-specific styling
    switch (type) {
      case 'thought':
        return { ...baseStyle, strokeDasharray: '5,5' }
      case 'whisper':
        return { ...baseStyle, strokeDasharray: '3,3', opacity: 0.8 }
      case 'shout':
        return { ...baseStyle, strokeWidth: outlineWidth * 1.5 }
      default:
        return baseStyle
    }
  }, [element.dialogueStyle])

  // Don't render queue for explosion type
  if (element.dialogueStyle.type === 'explosion') {
    return null
  }

  // ✅ DEBUG: Log queue rendering
  console.log('🎯 EnhancedBubbleQueue rendering:', {
    elementId: element.id,
    queueConfig,
    attachmentPoint,
    tipPosition,
    queuePath: queuePath.substring(0, 50) + '...'
  })

  return (
    <svg
      className={`enhanced-bubble-queue absolute ${className}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        overflow: 'visible',
        zIndex: 1 // Below bubble content but above background
      }}
    >
      {/* ✅ FUSION PARFAITE : Queue rendue SOUS la bulle */}
      <defs>
        <mask id={`queue-mask-${element.id}`}>
          <rect width="100%" height="100%" fill="white" />
          {/* Masque pour fusion douce */}
          <ellipse
            cx={width / 2}
            cy={height / 2}
            rx={width / 2 - 2}
            ry={height / 2 - 2}
            fill="black"
          />
        </mask>
      </defs>

      {/* Queue principale avec fusion */}
      <path
        d={queuePath}
        style={{
          ...queueStyle,
          // ✅ VISIBILITÉ : Garder le stroke pour debug
          stroke: queueStyle.stroke,
          strokeWidth: queueStyle.strokeWidth,
          fill: queueStyle.fill
        }}
        // ✅ TEMPORAIRE : Pas de masque pour debug
        // mask={queueConfig.seamlessConnection ? `url(#queue-mask-${element.id})` : undefined}
      />

      {/* ✅ PATCH DE FUSION : Cercle pour raccord parfait */}
      {queueConfig.seamlessConnection && (
        <ellipse
          cx={attachmentPoint.x}
          cy={attachmentPoint.y}
          rx={queueConfig.thickness / 2 + 1}
          ry={queueConfig.thickness / 2 + 1}
          style={{
            fill: queueStyle.fill,
            stroke: 'none'
          }}
        />
      )}
    </svg>
  )
}

// ✅ QUEUE GENERATION FUNCTIONS

/**
 * Generate triangular queue path (BD professionnelle avec pointe fine)
 */
function generateTriangleQueue(
  startX: number,
  startY: number,
  endX: number,
  endY: number,
  thickness: number,
  tapering: number = 0.8 // ✅ TAPERING FORT par défaut pour pointe fine
): string {
  const angle = Math.atan2(endY - startY, endX - startX)
  const perpAngle = angle + Math.PI / 2
  const distance = Math.sqrt((endX - startX) ** 2 + (endY - startY) ** 2)

  // ✅ CALCUL BD PROFESSIONNEL : Base large, pointe très fine
  const baseWidth = thickness
  const tipWidth = Math.max(1, thickness * (1 - tapering)) // Minimum 1px pour pointe visible

  // ✅ POINTS DE BASE avec léger recul pour fusion parfaite
  const baseInset = 3 // Recul dans la bulle pour fusion
  const baseStartX = startX - Math.cos(angle) * baseInset
  const baseStartY = startY - Math.sin(angle) * baseInset

  const base1X = baseStartX + Math.cos(perpAngle) * (baseWidth / 2)
  const base1Y = baseStartY + Math.sin(perpAngle) * (baseWidth / 2)
  const base2X = baseStartX - Math.cos(perpAngle) * (baseWidth / 2)
  const base2Y = baseStartY - Math.sin(perpAngle) * (baseWidth / 2)

  // ✅ POINTE ULTRA-FINE avec contrôle précis
  if (tipWidth <= 2) {
    // Pointe unique pour effet BD professionnel
    return `M ${base1X} ${base1Y} L ${endX} ${endY} L ${base2X} ${base2Y} Z`
  } else {
    // Pointe légèrement élargie pour queues épaisses
    const tip1X = endX + Math.cos(perpAngle) * (tipWidth / 2)
    const tip1Y = endY + Math.sin(perpAngle) * (tipWidth / 2)
    const tip2X = endX - Math.cos(perpAngle) * (tipWidth / 2)
    const tip2Y = endY - Math.sin(perpAngle) * (tipWidth / 2)

    // ✅ PATH AVEC COURBES DOUCES pour effet BD
    const controlDistance = distance * 0.3
    const control1X = base1X + Math.cos(angle) * controlDistance
    const control1Y = base1Y + Math.sin(angle) * controlDistance
    const control2X = base2X + Math.cos(angle) * controlDistance
    const control2Y = base2Y + Math.sin(angle) * controlDistance

    return `M ${base1X} ${base1Y} Q ${control1X} ${control1Y} ${tip1X} ${tip1Y} L ${endX} ${endY} L ${tip2X} ${tip2Y} Q ${control2X} ${control2Y} ${base2X} ${base2Y} Z`
  }
}

/**
 * Generate curved queue path (style BD pensée/souffle)
 */
function generateCurvedQueue(
  startX: number,
  startY: number,
  endX: number,
  endY: number,
  thickness: number,
  curvature: number = 0.4
): string {
  const angle = Math.atan2(endY - startY, endX - startX)
  const perpAngle = angle + Math.PI / 2
  const distance = Math.sqrt((endX - startX) ** 2 + (endY - startY) ** 2)

  // ✅ COURBE BD PROFESSIONNELLE avec multiple points de contrôle
  const baseInset = 3
  const baseStartX = startX - Math.cos(angle) * baseInset
  const baseStartY = startY - Math.sin(angle) * baseInset

  // Points de base avec fusion
  const base1X = baseStartX + Math.cos(perpAngle) * (thickness / 2)
  const base1Y = baseStartY + Math.sin(perpAngle) * (thickness / 2)
  const base2X = baseStartX - Math.cos(perpAngle) * (thickness / 2)
  const base2Y = baseStartY - Math.sin(perpAngle) * (thickness / 2)

  // ✅ POINTS DE CONTRÔLE MULTIPLES pour courbe naturelle BD
  const control1X = startX + Math.cos(angle) * distance * 0.3 + Math.cos(perpAngle) * distance * curvature
  const control1Y = startY + Math.sin(angle) * distance * 0.3 + Math.sin(perpAngle) * distance * curvature

  const control2X = startX + Math.cos(angle) * distance * 0.7 + Math.cos(perpAngle) * distance * curvature * 0.5
  const control2Y = startY + Math.sin(angle) * distance * 0.7 + Math.sin(perpAngle) * distance * curvature * 0.5

  // ✅ PATH COURBE SOPHISTIQUÉ avec Bézier cubique
  return `M ${base1X} ${base1Y}
          C ${control1X} ${control1Y} ${control2X} ${control2Y} ${endX} ${endY}
          C ${control2X - 5} ${control2Y + 5} ${control1X - 5} ${control1Y + 5} ${base2X} ${base2Y}
          Z`
}

/**
 * Generate jagged queue path (for shout bubbles)
 */
function generateJaggedQueue(
  startX: number, 
  startY: number, 
  endX: number, 
  endY: number, 
  thickness: number
): string {
  const distance = Math.sqrt((endX - startX) ** 2 + (endY - startY) ** 2)
  const angle = Math.atan2(endY - startY, endX - startX)
  const perpAngle = angle + Math.PI / 2
  
  const points = []
  const numSpikes = Math.max(3, Math.floor(distance / 15))
  
  for (let i = 0; i <= numSpikes; i++) {
    const progress = i / numSpikes
    const currentX = startX + (endX - startX) * progress
    const currentY = startY + (endY - startY) * progress
    
    // Alternate spike direction
    const spikeOffset = (i % 2 === 0) ? 0 : thickness * 0.3
    const pointX = currentX + Math.cos(perpAngle) * spikeOffset
    const pointY = currentY + Math.sin(perpAngle) * spikeOffset
    
    points.push(`${i === 0 ? 'M' : 'L'} ${pointX} ${pointY}`)
  }
  
  return points.join(' ')
}

/**
 * Generate thin queue path (for whisper bubbles)
 */
function generateThinQueue(
  startX: number, 
  startY: number, 
  endX: number, 
  endY: number, 
  thickness: number
): string {
  const thinThickness = thickness * 0.5
  const angle = Math.atan2(endY - startY, endX - startX)
  const perpAngle = angle + Math.PI / 2
  
  const base1X = startX + Math.cos(perpAngle) * (thinThickness / 2)
  const base1Y = startY + Math.sin(perpAngle) * (thinThickness / 2)
  const base2X = startX - Math.cos(perpAngle) * (thinThickness / 2)
  const base2Y = startY - Math.sin(perpAngle) * (thinThickness / 2)
  
  return `M ${base1X} ${base1Y} L ${endX} ${endY} L ${base2X} ${base2Y} Z`
}
