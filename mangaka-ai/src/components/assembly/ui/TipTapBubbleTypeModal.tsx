'use client'

// TipTapBubbleTypeModal - Modal pour sélectionner le type de bulle TipTap
// Interface moderne et intuitive pour choisir le style de bulle

import React from 'react'
import { BubbleType } from '../types/assembly.types'

interface TipTapBubbleTypeModalProps {
  isOpen: boolean
  onClose: () => void
  onSelectType: (type: BubbleType) => void
  currentType?: BubbleType
}

const BUBBLE_TYPES = [
  {
    type: 'speech' as BubbleType,
    name: 'Dialogue',
    icon: '💬',
    description: 'Bulle classique avec queue triangulaire',
    preview: 'Salut !',
    color: 'bg-blue-500',
    borderColor: 'border-blue-500'
  },
  {
    type: 'thought' as BubbleType,
    name: 'Pens<PERSON>',
    icon: '💭',
    description: 'Bulle ovale avec petites bulles',
    preview: 'Je pense que...',
    color: 'bg-purple-500',
    borderColor: 'border-purple-500'
  },
  {
    type: 'shout' as BubbleType,
    name: '<PERSON><PERSON>',
    icon: '💥',
    description: 'Contour en étoile/explosion',
    preview: 'AAAAH !',
    color: 'bg-red-500',
    borderColor: 'border-red-500'
  },
  {
    type: 'whisper' as BubbleType,
    name: 'Chuchotement',
    icon: '🤫',
    description: 'Contour en pointillés',
    preview: 'Psst...',
    color: 'bg-gray-500',
    borderColor: 'border-gray-500'
  },
  {
    type: 'explosion' as BubbleType,
    name: 'Explosion',
    icon: '💢',
    description: 'Effet explosif sans queue',
    preview: 'BOOM !',
    color: 'bg-orange-500',
    borderColor: 'border-orange-500'
  }
]

/**
 * Modal pour sélectionner le type de bulle TipTap
 * Interface moderne avec prévisualisations
 */
export default function TipTapBubbleTypeModal({
  isOpen,
  onClose,
  onSelectType,
  currentType = 'speech'
}: TipTapBubbleTypeModalProps) {

  console.log('🎯 TipTapBubbleTypeModal: Rendu avec isOpen =', isOpen)

  if (!isOpen) return null

  const handleSelectType = (type: BubbleType) => {
    console.log('🎯 TipTapBubbleTypeModal: Type sélectionné:', type)
    onSelectType(type)
    onClose()
  }

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center"
      style={{ zIndex: 9999 }} // Z-index très élevé pour être au-dessus de tout
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Choisir le type de bulle
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {BUBBLE_TYPES.map((bubbleType) => {
              const isSelected = currentType === bubbleType.type
              
              return (
                <button
                  key={bubbleType.type}
                  onClick={() => handleSelectType(bubbleType.type)}
                  className={`
                    relative p-4 rounded-lg border-2 transition-all duration-200 text-left
                    ${isSelected 
                      ? `${bubbleType.borderColor} bg-blue-50` 
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }
                  `}
                >
                  {/* Badge de sélection */}
                  {isSelected && (
                    <div className="absolute top-2 right-2">
                      <div className={`w-6 h-6 ${bubbleType.color} rounded-full flex items-center justify-center`}>
                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                  )}

                  {/* Icône et nom */}
                  <div className="flex items-center mb-2">
                    <span className="text-2xl mr-3">{bubbleType.icon}</span>
                    <div>
                      <h3 className="font-medium text-gray-900">{bubbleType.name}</h3>
                      <p className="text-sm text-gray-500">{bubbleType.description}</p>
                    </div>
                  </div>

                  {/* Prévisualisation */}
                  <div className="mt-3">
                    <div className={`
                      inline-block px-3 py-2 text-sm rounded-lg border-2
                      ${bubbleType.type === 'speech' ? 'rounded-2xl border-gray-800 bg-white' : ''}
                      ${bubbleType.type === 'thought' ? 'rounded-full border-gray-500 bg-white border-dashed' : ''}
                      ${bubbleType.type === 'shout' ? 'rounded-lg border-red-600 bg-yellow-50 border-4 font-bold' : ''}
                      ${bubbleType.type === 'whisper' ? 'rounded-xl border-gray-400 bg-gray-50 italic' : ''}
                      ${bubbleType.type === 'explosion' ? 'rounded-lg border-orange-600 bg-yellow-100 font-bold uppercase' : ''}
                    `}>
                      {bubbleType.preview}
                    </div>
                  </div>
                </button>
              )
            })}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            Annuler
          </button>
          <button
            onClick={() => handleSelectType(currentType)}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Confirmer
          </button>
        </div>
      </div>
    </div>
  )
}
