'use client'

// QueueManipulationHandles - Interactive handles for queue direction and length manipulation
// Features: Circular direction handle, radial length handle, real-time preview, snap-to-cardinal

import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react'
import { DialogueElement, QueueConfiguration } from '../types/assembly.types'

interface QueueManipulationHandlesProps {
  element: DialogueElement
  onQueueUpdate: (queueConfig: QueueConfiguration) => void
  isVisible: boolean
  className?: string
}

/**
 * Interactive handles for manipulating queue direction and length
 */
export default function QueueManipulationHandles({
  element,
  onQueueUpdate,
  isVisible,
  className = ''
}: QueueManipulationHandlesProps) {
  
  const [isDraggingDirection, setIsDraggingDirection] = useState(false)
  const [isDraggingLength, setIsDraggingLength] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0, initialAngle: 0, initialLength: 0 })

  const containerRef = useRef<HTMLDivElement>(null)

  // ✅ MIGRATION AUTOMATIQUE : Créer queue config si manquant
  const queueConfig = useMemo(() => {
    if (element.dialogueStyle.queue) {
      return element.dialogueStyle.queue
    }

    // Migration depuis legacy properties
    console.log('🔄 Migrating legacy queue properties for handles:', element.id)
    return {
      angle: element.dialogueStyle.tailAngleDegrees || 225,
      length: element.dialogueStyle.tailLength || 50,
      thickness: 24,
      style: 'triangle' as const,
      seamlessConnection: true,
      isManipulating: false,
      showHandles: false,
      snapToCardinal: false,
      curvature: 0.3,
      tapering: 0.85
    }
  }, [element.dialogueStyle])

  const { width, height } = element.transform

  // ✅ CALCULATE HANDLE POSITIONS
  const centerX = width / 2
  const centerY = height / 2
  const bubbleRadius = Math.min(width, height) / 2

  // ✅ CALCUL INTELLIGENT DES HANDLES BD
  const directionAngleRad = (queueConfig.angle * Math.PI) / 180

  // ✅ Handle de direction : sur cercle autour de la bulle (UX intuitive)
  const directionHandleRadius = Math.max(bubbleRadius + 25, 50) // Distance adaptative
  const directionHandleX = centerX + Math.cos(directionAngleRad) * directionHandleRadius
  const directionHandleY = centerY + Math.sin(directionAngleRad) * directionHandleRadius

  // ✅ Point d'attache intelligent (même calcul que EnhancedBubbleQueue)
  const radiusX = width / 2
  const radiusY = height / 2
  const cos = Math.cos(directionAngleRad)
  const sin = Math.sin(directionAngleRad)
  const denominator = Math.sqrt((radiusY * cos) ** 2 + (radiusX * sin) ** 2)

  const attachmentX = centerX + (radiusX * radiusY * cos) / denominator
  const attachmentY = centerY + (radiusX * radiusY * sin) / denominator

  // ✅ Handle de longueur : à la pointe de la queue
  const lengthHandleX = attachmentX + Math.cos(directionAngleRad) * queueConfig.length
  const lengthHandleY = attachmentY + Math.sin(directionAngleRad) * queueConfig.length

  // ✅ DIRECTION HANDLE DRAG HANDLERS
  const handleDirectionMouseDown = useCallback((event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()
    
    setIsDraggingDirection(true)
    setDragStart({
      x: event.clientX,
      y: event.clientY,
      initialAngle: queueConfig.angle,
      initialLength: queueConfig.length
    })
  }, [queueConfig.angle, queueConfig.length])

  // ✅ LENGTH HANDLE DRAG HANDLERS
  const handleLengthMouseDown = useCallback((event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()
    
    setIsDraggingLength(true)
    setDragStart({
      x: event.clientX,
      y: event.clientY,
      initialAngle: queueConfig.angle,
      initialLength: queueConfig.length
    })
  }, [queueConfig.angle, queueConfig.length])

  // ✅ GLOBAL MOUSE HANDLERS
  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      if (!containerRef.current) return

      const rect = containerRef.current.getBoundingClientRect()
      const mouseX = event.clientX - rect.left
      const mouseY = event.clientY - rect.top

      if (isDraggingDirection) {
        // Calculate angle from bubble center to mouse position
        const deltaX = mouseX - centerX
        const deltaY = mouseY - centerY
        let newAngle = (Math.atan2(deltaY, deltaX) * 180) / Math.PI
        
        // Normalize angle to 0-360
        if (newAngle < 0) newAngle += 360

        // Optional snap to cardinal directions
        if (queueConfig.snapToCardinal) {
          const cardinalAngles = [0, 90, 180, 270]
          const snapThreshold = 15 // degrees
          
          for (const cardinalAngle of cardinalAngles) {
            if (Math.abs(newAngle - cardinalAngle) < snapThreshold) {
              newAngle = cardinalAngle
              break
            }
          }
        }

        onQueueUpdate({
          ...queueConfig,
          angle: newAngle,
          isManipulating: true
        })
      }

      if (isDraggingLength) {
        // Calculate distance from attachment point to mouse
        const deltaX = mouseX - attachmentX
        const deltaY = mouseY - attachmentY
        const newLength = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
        
        // Constrain length to reasonable bounds
        const minLength = 20
        const maxLength = 200
        const constrainedLength = Math.max(minLength, Math.min(maxLength, newLength))

        onQueueUpdate({
          ...queueConfig,
          length: constrainedLength,
          isManipulating: true
        })
      }
    }

    const handleMouseUp = () => {
      if (isDraggingDirection || isDraggingLength) {
        setIsDraggingDirection(false)
        setIsDraggingLength(false)
        
        // End manipulation state
        onQueueUpdate({
          ...queueConfig,
          isManipulating: false
        })
      }
    }

    if (isDraggingDirection || isDraggingLength) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDraggingDirection, isDraggingLength, centerX, centerY, attachmentX, attachmentY, queueConfig, onQueueUpdate])

  // ✅ DEBUG: Log handles rendering
  console.log('🎮 QueueManipulationHandles:', {
    elementId: element.id,
    isVisible,
    queueConfig,
    directionHandlePos: { x: directionHandleX, y: directionHandleY },
    lengthHandlePos: { x: lengthHandleX, y: lengthHandleY }
  })

  if (!isVisible) return null

  return (
    <div
      ref={containerRef}
      className={`queue-manipulation-handles absolute ${className}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 10 // Above queue and bubble
      }}
    >
      {/* ✅ DIRECTION HANDLE - Style BD professionnel */}
      <div
        className="direction-handle"
        style={{
          position: 'absolute',
          left: directionHandleX - 10,
          top: directionHandleY - 10,
          width: 20,
          height: 20,
          background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
          border: '3px solid white',
          borderRadius: '50%',
          cursor: isDraggingDirection ? 'grabbing' : 'grab',
          pointerEvents: 'auto',
          boxShadow: '0 4px 8px rgba(0,0,0,0.3), inset 0 1px 2px rgba(255,255,255,0.3)',
          transform: isDraggingDirection ? 'scale(1.3)' : 'scale(1)',
          transition: isDraggingDirection ? 'none' : 'all 0.2s ease',
          zIndex: 20
        }}
        onMouseDown={handleDirectionMouseDown}
        title="🎯 Glisser pour orienter la queue (360°)"
      >
        {/* Icône directionnelle */}
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 0,
          height: 0,
          borderLeft: '3px solid transparent',
          borderRight: '3px solid transparent',
          borderBottom: '6px solid white',
          transform: `translate(-50%, -50%) rotate(${queueConfig.angle - 90}deg)`
        }} />
      </div>

      {/* ✅ LENGTH HANDLE - Style BD professionnel */}
      <div
        className="length-handle"
        style={{
          position: 'absolute',
          left: lengthHandleX - 8,
          top: lengthHandleY - 8,
          width: 16,
          height: 16,
          background: 'linear-gradient(135deg, #10b981, #047857)',
          border: '2px solid white',
          borderRadius: '50%',
          cursor: isDraggingLength ? 'grabbing' : 'grab',
          pointerEvents: 'auto',
          boxShadow: '0 3px 6px rgba(0,0,0,0.25), inset 0 1px 2px rgba(255,255,255,0.3)',
          transform: isDraggingLength ? 'scale(1.3)' : 'scale(1)',
          transition: isDraggingLength ? 'none' : 'all 0.2s ease',
          zIndex: 20
        }}
        onMouseDown={handleLengthMouseDown}
        title="📏 Glisser pour ajuster la longueur"
      >
        {/* Icône de redimensionnement */}
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 8,
          height: 2,
          backgroundColor: 'white',
          borderRadius: 1
        }} />
      </div>

      {/* ✅ GUIDE VISUEL BD PROFESSIONNEL */}
      <svg
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          opacity: isDraggingDirection ? 0.4 : 0,
          transition: 'opacity 0.2s ease'
        }}
      >
        {/* Cercle de direction avec style BD */}
        <circle
          cx={centerX}
          cy={centerY}
          r={directionHandleRadius}
          fill="none"
          stroke="#3b82f6"
          strokeWidth="2"
          strokeDasharray="8,4"
          opacity="0.6"
        />

        {/* Ligne de direction actuelle */}
        <line
          x1={centerX}
          y1={centerY}
          x2={directionHandleX}
          y2={directionHandleY}
          stroke="#3b82f6"
          strokeWidth="2"
          strokeDasharray="4,2"
          opacity="0.8"
        />

        {/* Indicateur d'angle */}
        <text
          x={centerX + 30}
          y={centerY - 30}
          fill="#3b82f6"
          fontSize="12"
          fontWeight="bold"
          textAnchor="middle"
        >
          {Math.round(queueConfig.angle)}°
        </text>
      </svg>

      {/* Length Guide Line */}
      <svg
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          opacity: isDraggingLength ? 0.3 : 0
        }}
      >
        <line
          x1={attachmentX}
          y1={attachmentY}
          x2={lengthHandleX}
          y2={lengthHandleY}
          stroke="#10b981"
          strokeWidth="2"
          strokeDasharray="3,3"
        />
      </svg>

      {/* Cardinal Direction Indicators (when snap is enabled) */}
      {queueConfig.snapToCardinal && (
        <svg
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            pointerEvents: 'none',
            opacity: isDraggingDirection ? 0.2 : 0
          }}
        >
          {[0, 90, 180, 270].map(angle => {
            const angleRad = (angle * Math.PI) / 180
            const x = centerX + Math.cos(angleRad) * directionHandleRadius
            const y = centerY + Math.sin(angleRad) * directionHandleRadius
            
            return (
              <circle
                key={angle}
                cx={x}
                cy={y}
                r="3"
                fill="#f59e0b"
                stroke="white"
                strokeWidth="1"
              />
            )
          })}
        </svg>
      )}
    </div>
  )
}
