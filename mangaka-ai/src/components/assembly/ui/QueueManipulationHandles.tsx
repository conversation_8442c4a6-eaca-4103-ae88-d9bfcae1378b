'use client'

// QueueManipulationHandles - Interactive handles for queue direction and length manipulation
// Features: Circular direction handle, radial length handle, real-time preview, snap-to-cardinal

import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react'
import { DialogueElement, QueueConfiguration } from '../types/assembly.types'

interface QueueManipulationHandlesProps {
  element: DialogueElement
  onQueueUpdate: (queueConfig: QueueConfiguration) => void
  isVisible: boolean
  className?: string
}

/**
 * Interactive handles for manipulating queue direction and length
 */
export default function QueueManipulationHandles({
  element,
  onQueueUpdate,
  isVisible,
  className = ''
}: QueueManipulationHandlesProps) {
  
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0, initialAngle: 0 })

  const containerRef = useRef<HTMLDivElement>(null)

  // ✅ MIGRATION AUTOMATIQUE : Créer queue config si manquant
  const queueConfig = useMemo(() => {
    if (element.dialogueStyle.queue) {
      return element.dialogueStyle.queue
    }

    // Migration depuis legacy properties
    console.log('🔄 Migrating legacy queue properties for handles:', element.id)
    return {
      angle: element.dialogueStyle.tailAngleDegrees || 225,
      length: element.dialogueStyle.tailLength || 50,
      thickness: 24,
      style: 'triangle' as const,
      seamlessConnection: true,
      isManipulating: false,
      showHandles: false,
      snapToCardinal: false,
      curvature: 0.3,
      tapering: 0.85
    }
  }, [element.dialogueStyle])

  const { width, height } = element.transform

  // ✅ CALCULATE HANDLE POSITIONS
  const centerX = width / 2
  const centerY = height / 2
  const bubbleRadius = Math.min(width, height) / 2

  // ✅ CALCUL INTELLIGENT DES HANDLES BD
  const directionAngleRad = (queueConfig.angle * Math.PI) / 180

  // ✅ Handle de direction : sur cercle autour de la bulle (UX intuitive)
  const directionHandleRadius = Math.max(bubbleRadius + 25, 50) // Distance adaptative
  const directionHandleX = centerX + Math.cos(directionAngleRad) * directionHandleRadius
  const directionHandleY = centerY + Math.sin(directionAngleRad) * directionHandleRadius

  // ✅ Point d'attache intelligent (même calcul que EnhancedBubbleQueue)
  const radiusX = width / 2
  const radiusY = height / 2
  const cos = Math.cos(directionAngleRad)
  const sin = Math.sin(directionAngleRad)
  const denominator = Math.sqrt((radiusY * cos) ** 2 + (radiusX * sin) ** 2)

  const attachmentX = centerX + (radiusX * radiusY * cos) / denominator
  const attachmentY = centerY + (radiusX * radiusY * sin) / denominator

  // ✅ HANDLE BLEU : rotation 360° uniquement
  const handleX = attachmentX + Math.cos(directionAngleRad) * queueConfig.length
  const handleY = attachmentY + Math.sin(directionAngleRad) * queueConfig.length

  // ✅ HANDLE ROTATION 360° UNIQUEMENT
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()

    setIsDragging(true)
    setDragStart({
      x: event.clientX,
      y: event.clientY,
      initialAngle: queueConfig.angle
    })
  }, [queueConfig.angle])

  // ✅ GLOBAL MOUSE HANDLERS
  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      if (!containerRef.current) return

      const rect = containerRef.current.getBoundingClientRect()
      const mouseX = event.clientX - rect.left
      const mouseY = event.clientY - rect.top

      if (isDragging) {
        // ✅ ROTATION 360° UNIQUEMENT : angle depuis le centre de la bulle
        const deltaX = mouseX - centerX
        const deltaY = mouseY - centerY
        let newAngle = (Math.atan2(deltaY, deltaX) * 180) / Math.PI

        if (newAngle < 0) newAngle += 360

        onQueueUpdate({
          ...queueConfig,
          angle: newAngle,
          isManipulating: true
        })
      }
    }

    const handleMouseUp = () => {
      if (isDragging) {
        setIsDragging(false)

        onQueueUpdate({
          ...queueConfig,
          isManipulating: false
        })
      }
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)

      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, centerX, centerY, queueConfig, onQueueUpdate])

  if (!isVisible) return null

  return (
    <div
      ref={containerRef}
      className={`queue-manipulation-handles absolute ${className}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 10
      }}
    >
      {/* ✅ HANDLE UNIQUE SIMPLIFIÉ */}
      <div
        style={{
          position: 'absolute',
          left: handleX - 6,
          top: handleY - 6,
          width: 12,
          height: 12,
          background: '#3b82f6',
          border: '2px solid white',
          borderRadius: '50%',
          cursor: isDragging ? 'grabbing' : 'grab',
          pointerEvents: 'auto',
          boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
          zIndex: 100
        }}
        onMouseDown={handleMouseDown}
      />

    </div>
  )
}
