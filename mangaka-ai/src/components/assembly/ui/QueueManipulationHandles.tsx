'use client'

// QueueManipulationHandles - Interactive handles for queue direction and length manipulation
// Features: Circular direction handle, radial length handle, real-time preview, snap-to-cardinal

import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react'
import { DialogueElement, QueueConfiguration } from '../types/assembly.types'

interface QueueManipulationHandlesProps {
  element: DialogueElement
  onQueueUpdate: (queueConfig: QueueConfiguration) => void
  isVisible: boolean
  className?: string
}

/**
 * Interactive handles for manipulating queue direction and length
 */
export default function QueueManipulationHandles({
  element,
  onQueueUpdate,
  isVisible,
  className = ''
}: QueueManipulationHandlesProps) {
  
  const [isDraggingRotation, setIsDraggingRotation] = useState(false)
  const [isDraggingLength, setIsDraggingLength] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0, initialAngle: 0, initialLength: 0 })

  const containerRef = useRef<HTMLDivElement>(null)

  // ✅ MIGRATION AUTOMATIQUE : Créer queue config si manquant
  const queueConfig = useMemo(() => {
    if (element.dialogueStyle.queue) {
      return element.dialogueStyle.queue
    }

    // Migration depuis legacy properties
    console.log('🔄 Migrating legacy queue properties for handles:', element.id)
    return {
      angle: element.dialogueStyle.tailAngleDegrees || 225,
      length: element.dialogueStyle.tailLength || 50,
      thickness: 24,
      style: 'triangle' as const,
      seamlessConnection: true,
      isManipulating: false,
      showHandles: false,
      snapToCardinal: false,
      curvature: 0.3,
      tapering: 0.85
    }
  }, [element.dialogueStyle])

  const { width, height } = element.transform

  // ✅ CALCULATE HANDLE POSITIONS
  const centerX = width / 2
  const centerY = height / 2
  const bubbleRadius = Math.min(width, height) / 2

  // ✅ CALCUL INTELLIGENT DES HANDLES BD
  const directionAngleRad = (queueConfig.angle * Math.PI) / 180

  // ✅ Handle de direction : sur cercle autour de la bulle (UX intuitive)
  const directionHandleRadius = Math.max(bubbleRadius + 25, 50) // Distance adaptative
  const directionHandleX = centerX + Math.cos(directionAngleRad) * directionHandleRadius
  const directionHandleY = centerY + Math.sin(directionAngleRad) * directionHandleRadius

  // ✅ Point d'attache intelligent (même calcul que EnhancedBubbleQueue)
  const radiusX = width / 2
  const radiusY = height / 2
  const cos = Math.cos(directionAngleRad)
  const sin = Math.sin(directionAngleRad)
  const denominator = Math.sqrt((radiusY * cos) ** 2 + (radiusX * sin) ** 2)

  const attachmentX = centerX + (radiusX * radiusY * cos) / denominator
  const attachmentY = centerY + (radiusX * radiusY * sin) / denominator

  // ✅ HANDLE BLEU : contrôle de la TAILLE (au bout de la queue)
  const lengthHandleX = attachmentX + Math.cos(directionAngleRad) * queueConfig.length
  const lengthHandleY = attachmentY + Math.sin(directionAngleRad) * queueConfig.length

  // ✅ HANDLE ROTATION : au milieu de la queue
  const rotationHandleX = attachmentX + Math.cos(directionAngleRad) * (queueConfig.length * 0.6)
  const rotationHandleY = attachmentY + Math.sin(directionAngleRad) * (queueConfig.length * 0.6)

  // ✅ HANDLE ROTATION (milieu)
  const handleRotationMouseDown = useCallback((event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()

    setIsDraggingRotation(true)
    setDragStart({
      x: event.clientX,
      y: event.clientY,
      initialAngle: queueConfig.angle,
      initialLength: queueConfig.length
    })
  }, [queueConfig.angle, queueConfig.length])

  // ✅ HANDLE TAILLE (bout)
  const handleLengthMouseDown = useCallback((event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()

    setIsDraggingLength(true)
    setDragStart({
      x: event.clientX,
      y: event.clientY,
      initialAngle: queueConfig.angle,
      initialLength: queueConfig.length
    })
  }, [queueConfig.angle, queueConfig.length])

  // ✅ GLOBAL MOUSE HANDLERS
  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      if (!containerRef.current) return

      const rect = containerRef.current.getBoundingClientRect()
      const mouseX = event.clientX - rect.left
      const mouseY = event.clientY - rect.top

      if (isDraggingRotation) {
        // ✅ ROTATION 360° : angle depuis le centre de la bulle
        const deltaX = mouseX - centerX
        const deltaY = mouseY - centerY
        let newAngle = (Math.atan2(deltaY, deltaX) * 180) / Math.PI

        if (newAngle < 0) newAngle += 360

        onQueueUpdate({
          ...queueConfig,
          angle: newAngle,
          isManipulating: true
        })
      }

      if (isDraggingLength) {
        // ✅ CONTRÔLE DE LA TAILLE : distance depuis le point d'attache
        const deltaX = mouseX - attachmentX
        const deltaY = mouseY - attachmentY
        const newLength = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

        const minLength = 20
        const maxLength = 150
        const constrainedLength = Math.max(minLength, Math.min(maxLength, newLength))

        onQueueUpdate({
          ...queueConfig,
          length: constrainedLength,
          isManipulating: true
        })
      }
    }

    const handleMouseUp = () => {
      if (isDraggingRotation || isDraggingLength) {
        setIsDraggingRotation(false)
        setIsDraggingLength(false)

        onQueueUpdate({
          ...queueConfig,
          isManipulating: false
        })
      }
    }

    if (isDraggingRotation || isDraggingLength) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)

      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDraggingRotation, isDraggingLength, centerX, centerY, attachmentX, attachmentY, queueConfig, onQueueUpdate])

  // ✅ DEBUG: Vérifier la visibilité
  console.log('🎮 QueueManipulationHandles:', {
    elementId: element.id,
    isVisible,
    willRender: isVisible
  })

  if (!isVisible) return null

  return (
    <div
      ref={containerRef}
      className={`queue-manipulation-handles absolute ${className}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 10
      }}
    >
      {/* ✅ HANDLE ROTATION (milieu de la queue) */}
      <div
        style={{
          position: 'absolute',
          left: rotationHandleX - 8,
          top: rotationHandleY - 8,
          width: 16,
          height: 16,
          background: '#f59e0b', // Orange pour rotation
          border: '2px solid white',
          borderRadius: '50%',
          cursor: isDraggingRotation ? 'grabbing' : 'grab',
          pointerEvents: 'auto',
          boxShadow: '0 2px 4px rgba(0,0,0,0.3)',
          zIndex: 100
        }}
        onMouseDown={handleRotationMouseDown}
        title="Rotation 360°"
      />

      {/* ✅ HANDLE TAILLE (bout de la queue) */}
      <div
        style={{
          position: 'absolute',
          left: lengthHandleX - 6,
          top: lengthHandleY - 6,
          width: 12,
          height: 12,
          background: '#3b82f6', // Bleu pour taille
          border: '2px solid white',
          borderRadius: '50%',
          cursor: isDraggingLength ? 'grabbing' : 'grab',
          pointerEvents: 'auto',
          boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
          zIndex: 100
        }}
        onMouseDown={handleLengthMouseDown}
        title="Contrôle de la taille"
      />

    </div>
  )
}
