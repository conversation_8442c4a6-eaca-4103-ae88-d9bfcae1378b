'use client'

// QueueManipulationHandles - Interactive handles for queue direction and length manipulation
// Features: Circular direction handle, radial length handle, real-time preview, snap-to-cardinal

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { DialogueElement, QueueConfiguration } from '../types/assembly.types'

interface QueueManipulationHandlesProps {
  element: DialogueElement
  onQueueUpdate: (queueConfig: QueueConfiguration) => void
  isVisible: boolean
  className?: string
}

/**
 * Interactive handles for manipulating queue direction and length
 */
export default function QueueManipulationHandles({
  element,
  onQueueUpdate,
  isVisible,
  className = ''
}: QueueManipulationHandlesProps) {
  
  const [isDraggingDirection, setIsDraggingDirection] = useState(false)
  const [isDraggingLength, setIsDraggingLength] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0, initialAngle: 0, initialLength: 0 })
  
  const containerRef = useRef<HTMLDivElement>(null)
  const queueConfig = element.dialogueStyle.queue
  const { width, height } = element.transform

  // ✅ CALCULATE HANDLE POSITIONS
  const centerX = width / 2
  const centerY = height / 2
  const bubbleRadius = Math.min(width, height) / 2

  // Direction handle position (on bubble perimeter)
  const directionHandleRadius = bubbleRadius + 20
  const directionAngleRad = (queueConfig.angle * Math.PI) / 180
  const directionHandleX = centerX + Math.cos(directionAngleRad) * directionHandleRadius
  const directionHandleY = centerY + Math.sin(directionAngleRad) * directionHandleRadius

  // Length handle position (at queue tip)
  const attachmentX = centerX + Math.cos(directionAngleRad) * bubbleRadius * 0.9
  const attachmentY = centerY + Math.sin(directionAngleRad) * bubbleRadius * 0.9
  const lengthHandleX = attachmentX + Math.cos(directionAngleRad) * queueConfig.length
  const lengthHandleY = attachmentY + Math.sin(directionAngleRad) * queueConfig.length

  // ✅ DIRECTION HANDLE DRAG HANDLERS
  const handleDirectionMouseDown = useCallback((event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()
    
    setIsDraggingDirection(true)
    setDragStart({
      x: event.clientX,
      y: event.clientY,
      initialAngle: queueConfig.angle,
      initialLength: queueConfig.length
    })
  }, [queueConfig.angle, queueConfig.length])

  // ✅ LENGTH HANDLE DRAG HANDLERS
  const handleLengthMouseDown = useCallback((event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()
    
    setIsDraggingLength(true)
    setDragStart({
      x: event.clientX,
      y: event.clientY,
      initialAngle: queueConfig.angle,
      initialLength: queueConfig.length
    })
  }, [queueConfig.angle, queueConfig.length])

  // ✅ GLOBAL MOUSE HANDLERS
  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      if (!containerRef.current) return

      const rect = containerRef.current.getBoundingClientRect()
      const mouseX = event.clientX - rect.left
      const mouseY = event.clientY - rect.top

      if (isDraggingDirection) {
        // Calculate angle from bubble center to mouse position
        const deltaX = mouseX - centerX
        const deltaY = mouseY - centerY
        let newAngle = (Math.atan2(deltaY, deltaX) * 180) / Math.PI
        
        // Normalize angle to 0-360
        if (newAngle < 0) newAngle += 360

        // Optional snap to cardinal directions
        if (queueConfig.snapToCardinal) {
          const cardinalAngles = [0, 90, 180, 270]
          const snapThreshold = 15 // degrees
          
          for (const cardinalAngle of cardinalAngles) {
            if (Math.abs(newAngle - cardinalAngle) < snapThreshold) {
              newAngle = cardinalAngle
              break
            }
          }
        }

        onQueueUpdate({
          ...queueConfig,
          angle: newAngle,
          isManipulating: true
        })
      }

      if (isDraggingLength) {
        // Calculate distance from attachment point to mouse
        const deltaX = mouseX - attachmentX
        const deltaY = mouseY - attachmentY
        const newLength = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
        
        // Constrain length to reasonable bounds
        const minLength = 20
        const maxLength = 200
        const constrainedLength = Math.max(minLength, Math.min(maxLength, newLength))

        onQueueUpdate({
          ...queueConfig,
          length: constrainedLength,
          isManipulating: true
        })
      }
    }

    const handleMouseUp = () => {
      if (isDraggingDirection || isDraggingLength) {
        setIsDraggingDirection(false)
        setIsDraggingLength(false)
        
        // End manipulation state
        onQueueUpdate({
          ...queueConfig,
          isManipulating: false
        })
      }
    }

    if (isDraggingDirection || isDraggingLength) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDraggingDirection, isDraggingLength, centerX, centerY, attachmentX, attachmentY, queueConfig, onQueueUpdate])

  if (!isVisible) return null

  return (
    <div
      ref={containerRef}
      className={`queue-manipulation-handles absolute ${className}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 10 // Above queue and bubble
      }}
    >
      {/* Direction Handle - Circular control around bubble */}
      <div
        className="direction-handle"
        style={{
          position: 'absolute',
          left: directionHandleX - 8,
          top: directionHandleY - 8,
          width: 16,
          height: 16,
          backgroundColor: '#3b82f6',
          border: '2px solid white',
          borderRadius: '50%',
          cursor: 'grab',
          pointerEvents: 'auto',
          boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
          transform: isDraggingDirection ? 'scale(1.2)' : 'scale(1)',
          transition: isDraggingDirection ? 'none' : 'transform 0.2s ease'
        }}
        onMouseDown={handleDirectionMouseDown}
        title="Drag to change queue direction"
      />

      {/* Length Handle - At queue tip */}
      <div
        className="length-handle"
        style={{
          position: 'absolute',
          left: lengthHandleX - 6,
          top: lengthHandleY - 6,
          width: 12,
          height: 12,
          backgroundColor: '#10b981',
          border: '2px solid white',
          borderRadius: '50%',
          cursor: 'grab',
          pointerEvents: 'auto',
          boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
          transform: isDraggingLength ? 'scale(1.2)' : 'scale(1)',
          transition: isDraggingLength ? 'none' : 'transform 0.2s ease'
        }}
        onMouseDown={handleLengthMouseDown}
        title="Drag to change queue length"
      />

      {/* Direction Guide Circle */}
      <svg
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          opacity: isDraggingDirection ? 0.3 : 0
        }}
      >
        <circle
          cx={centerX}
          cy={centerY}
          r={directionHandleRadius}
          fill="none"
          stroke="#3b82f6"
          strokeWidth="1"
          strokeDasharray="5,5"
        />
      </svg>

      {/* Length Guide Line */}
      <svg
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          opacity: isDraggingLength ? 0.3 : 0
        }}
      >
        <line
          x1={attachmentX}
          y1={attachmentY}
          x2={lengthHandleX}
          y2={lengthHandleY}
          stroke="#10b981"
          strokeWidth="2"
          strokeDasharray="3,3"
        />
      </svg>

      {/* Cardinal Direction Indicators (when snap is enabled) */}
      {queueConfig.snapToCardinal && (
        <svg
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            pointerEvents: 'none',
            opacity: isDraggingDirection ? 0.2 : 0
          }}
        >
          {[0, 90, 180, 270].map(angle => {
            const angleRad = (angle * Math.PI) / 180
            const x = centerX + Math.cos(angleRad) * directionHandleRadius
            const y = centerY + Math.sin(angleRad) * directionHandleRadius
            
            return (
              <circle
                key={angle}
                cx={x}
                cy={y}
                r="3"
                fill="#f59e0b"
                stroke="white"
                strokeWidth="1"
              />
            )
          })}
        </svg>
      )}
    </div>
  )
}
