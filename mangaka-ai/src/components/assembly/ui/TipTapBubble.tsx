'use client'

// TipTapBubble - Système de speech bubbles avec manipulation complète
// Architecture: TipTap + système de manipulation identique aux panels

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import { DialogueElement, BubbleType } from '../types/assembly.types'
// ✅ SUPPRIMÉ : TipTapBubbleManipulator - manipulation gérée par SimpleCanvasEditor
import BubbleQueue from './BubbleQueue'

// Types pour les modes UX
export type BubbleMode = 'reading' | 'editing' | 'manipulating'

interface TipTapBubbleProps {
  element: DialogueElement
  isSelected: boolean
  mode: BubbleMode
  onUpdate: (bubbleId: string, updates: Partial<DialogueElement>) => void
  onDoubleClick?: (bubbleId: string) => void
  onModeChange?: (bubbleId: string, newMode: BubbleMode) => void
  // ✅ NOUVEAU : onModeChange pour gérer les transitions de modes
}

/**
 * Nouveau système de speech bubbles basé sur TipTap
 * Trois modes UX distincts : lecture, édition, manipulation
 */
export default function TipTapBubble({
  element,
  isSelected,
  mode,
  onUpdate,
  onDoubleClick,
  onModeChange
}: TipTapBubbleProps) {
  const bubbleRef = useRef<HTMLDivElement>(null)
  const editorRef = useRef<any>(null)

  // États locaux
  const [isHovered, setIsHovered] = useState(false)
  const [contentSize, setContentSize] = useState({ width: 0, height: 0 })

  // ✅ TIPTAP COMME CŒUR DU SYSTÈME
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Configuration optimisée pour les speech bubbles
        heading: false,
        blockquote: false,
        codeBlock: false,
        horizontalRule: false,
        listItem: false,
        orderedList: false,
        bulletList: false,
      }),
      Placeholder.configure({
        placeholder: 'Tapez votre texte...',
      }),
    ],
    content: element.text || '',
    editable: mode === 'editing',
    immediatelyRender: false,
    shouldRerenderOnTransaction: false,
    editorProps: {
      attributes: {
        class: 'tiptap-bubble-editor',
        style: 'outline: none; border: none; padding: 8px; margin: 0; width: 100%; height: 100%;'
      },
    },
    onUpdate: ({ editor }) => {
      const newText = editor.getHTML()
      onUpdate(element.id, { text: newText })
      
      // Auto-redimensionnement selon le contenu
      measureContent()
    },
    onFocus: () => {
      console.log(`📝 TipTap focused for bubble: ${element.id}`)
    },
    onBlur: () => {
      console.log(`📝 TipTap blurred for bubble: ${element.id}`)
      if (mode === 'editing') {
        // Délai pour permettre le clic sur autre chose
        setTimeout(() => {
          onModeChange?.(element.id, 'reading')
          // Dispatcher l'événement pour SimpleCanvasEditor
          const modeChangeEvent = new CustomEvent('bubbleModeChangeFromBubble', {
            detail: { bubbleId: element.id, newMode: 'reading' }
          })
          window.dispatchEvent(modeChangeEvent)
        }, 100)
      }
    },
  })

  // ✅ AUTO-REDIMENSIONNEMENT SELON LE CONTENU (optimisé pour éviter le flash)
  const measureContent = useCallback(() => {
    if (!editor || !bubbleRef.current) return

    const textContent = editor.getText()

    // ✅ CORRECTION : Ne pas redimensionner si le texte est vide (nouvelle bulle)
    if (!textContent || textContent.trim() === '') {
      return
    }

    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!

    ctx.font = `${element.dialogueStyle.fontSize}px ${element.dialogueStyle.fontFamily}`
    const textMetrics = ctx.measureText(textContent)

    const padding = 16
    const minWidth = 100
    const minHeight = 60
    const maxWidth = 300

    const newWidth = Math.min(Math.max(textMetrics.width + padding * 2, minWidth), maxWidth)
    const newHeight = Math.max(element.dialogueStyle.fontSize * 1.5 + padding * 2, minHeight)

    setContentSize({ width: newWidth, height: newHeight })

    // ✅ CORRECTION : Seuil plus élevé pour éviter les micro-ajustements
    if (Math.abs(element.transform.width - newWidth) > 10 ||
        Math.abs(element.transform.height - newHeight) > 10) {
      onUpdate(element.id, {
        transform: {
          ...element.transform,
          width: newWidth,
          height: newHeight
        }
      })
    }
  }, [editor, element, onUpdate])

  // ✅ FOCUS AUTOMATIQUE EN MODE ÉDITION
  useEffect(() => {
    if (mode === 'editing' && editor) {
      // Focus sur l'éditeur quand on passe en mode édition
      setTimeout(() => {
        editor.commands.focus()
      }, 100)
    }
  }, [mode, editor])

  // ✅ GESTION DES RACCOURCIS CLAVIER
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (mode === 'editing' && e.key === 'Escape') {
        e.preventDefault()
        console.log('🎯 TipTapBubble: Escape pressed, exiting edit mode')
        onModeChange?.(element.id, 'reading')
        // Dispatcher l'événement pour SimpleCanvasEditor
        const modeChangeEvent = new CustomEvent('bubbleModeChangeFromBubble', {
          detail: { bubbleId: element.id, newMode: 'reading' }
        })
        window.dispatchEvent(modeChangeEvent)
      }
    }

    if (mode === 'editing') {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [mode, element.id, onModeChange])

  // ✅ SUPPRIMÉ : Le positionnement est maintenant géré par TipTapBubbleManipulator

  // ✅ STYLES DE BULLE SELON LE TYPE
  const getBubbleClasses = useCallback(() => {
    const baseClasses = 'tiptap-bubble flex items-center justify-center border-2 transition-all duration-200'
    const modeClasses = {
      reading: '',
      editing: 'ring-2 ring-blue-500 ring-opacity-50',
      manipulating: 'ring-2 ring-green-500 ring-opacity-50'
    }
    const hoverClasses = isHovered && mode !== 'editing' ? 'shadow-lg' : ''

    // Styles selon le type de bulle
    const typeClasses = {
      speech: 'rounded-2xl border-gray-800 bg-white shadow-md',
      thought: 'rounded-full border-gray-500 bg-white border-dashed shadow-sm',
      shout: 'rounded-lg border-red-600 bg-yellow-50 shadow-lg border-4',
      whisper: 'rounded-xl border-gray-400 bg-gray-50 shadow-sm',
      explosion: 'rounded-lg border-orange-600 bg-yellow-100 shadow-lg'
    }

    return `${baseClasses} ${modeClasses[mode]} ${hoverClasses} ${typeClasses[element.dialogueStyle.type]}`
  }, [mode, isHovered, element.dialogueStyle.type])

  // ✅ STYLES DE TEXTE
  const textStyle = useMemo(() => ({
    fontSize: `${element.dialogueStyle.fontSize}px`,
    fontFamily: element.dialogueStyle.fontFamily,
    color: typeof element.dialogueStyle.textColor === 'string' 
      ? element.dialogueStyle.textColor 
      : `#${element.dialogueStyle.textColor.toString(16).padStart(6, '0')}`,
    textAlign: element.dialogueStyle.textAlign as 'left' | 'center' | 'right',
    lineHeight: 1.2,
    wordBreak: 'break-word' as const
  }), [element.dialogueStyle])

  // ✅ NOUVEAU : Écouter les événements de changement de mode
  useEffect(() => {
    const handleModeChange = (event: CustomEvent) => {
      const { bubbleId, newMode } = event.detail
      if (bubbleId === element.id) {
        console.log('🎯 TipTapBubble: Mode change reçu:', bubbleId, newMode)
        onModeChange?.(bubbleId, newMode)

        // Si passage en mode édition, focus sur l'éditeur
        if (newMode === 'editing' && editor) {
          setTimeout(() => {
            editor.commands.focus()
            console.log('🎯 TipTapBubble: Focus sur éditeur:', bubbleId)
          }, 100)
        }
      }
    }

    window.addEventListener('bubbleModeChange', handleModeChange as EventListener)

    return () => {
      window.removeEventListener('bubbleModeChange', handleModeChange as EventListener)
    }
  }, [element.id, editor, onModeChange])

  // ✅ NOUVEAU : Enregistrer la bulle dans SimpleCanvasEditor pour la détection
  useEffect(() => {
    if (bubbleRef.current) {
      const bounds = bubbleRef.current.getBoundingClientRect()
      const registerEvent = new CustomEvent('registerTipTapBubble', {
        detail: {
          bubbleId: element.id,
          element: bubbleRef.current,
          bounds: bounds
        }
      })
      window.dispatchEvent(registerEvent)
      console.log('📝 TipTapBubble enregistré:', element.id)

      // Cleanup au démontage
      return () => {
        const unregisterEvent = new CustomEvent('unregisterTipTapBubble', {
          detail: { bubbleId: element.id }
        })
        window.dispatchEvent(unregisterEvent)
        console.log('🗑️ TipTapBubble désenregistré:', element.id)
      }
    }
  }, [element.id])

  // ✅ NOUVEAU : Mettre à jour les bounds quand la position change
  useEffect(() => {
    if (bubbleRef.current) {
      const bounds = bubbleRef.current.getBoundingClientRect()
      const updateEvent = new CustomEvent('updateTipTapBubbleBounds', {
        detail: {
          bubbleId: element.id,
          bounds: bounds
        }
      })
      window.dispatchEvent(updateEvent)
    }
  }, [element.transform.x, element.transform.y, element.transform.width, element.transform.height])

  // ✅ CORRECTION : Mesurer le contenu seulement quand il y a du texte
  useEffect(() => {
    // Ne pas mesurer immédiatement au montage pour éviter le flash
    if (element.text && element.text.trim() !== '') {
      // Délai court pour laisser le temps au DOM de se stabiliser
      const timeoutId = setTimeout(() => {
        measureContent()
      }, 50)

      return () => clearTimeout(timeoutId)
    }
  }, [measureContent, element.text])

  // ✅ NOUVEAU : Event handlers pour SimpleCanvasEditor
  const handleBubbleClick = useCallback((event: React.MouseEvent) => {
    event.stopPropagation()
    console.log('🎯 TipTapBubble: Forwarding click to SimpleCanvasEditor:', element.id)

    // Dispatcher un événement personnalisé pour SimpleCanvasEditor
    const bubbleClickEvent = new CustomEvent('bubbleClicked', {
      detail: {
        bubbleId: element.id,
        clientX: event.clientX,
        clientY: event.clientY,
        element: bubbleRef.current
      }
    })
    window.dispatchEvent(bubbleClickEvent)
  }, [element.id])

  const handleBubbleDoubleClick = useCallback((event: React.MouseEvent) => {
    event.stopPropagation()
    console.log('🎯 TipTapBubble: Double-click pour édition:', element.id)
    onDoubleClick?.(element.id)
  }, [element.id, onDoubleClick])

  return (
    <div
      style={{
        position: 'absolute',
        left: element.transform.x,
        top: element.transform.y,
        width: element.transform.width,
        height: element.transform.height,
        zIndex: element.transform.zIndex || 200,
        pointerEvents: 'auto' // ✅ PERMETTRE LES CLICS
      }}
    >
      {/* Queue de la bulle */}
      <BubbleQueue element={element} />

      {/* Contenu de la bulle */}
      <div
        ref={bubbleRef}
        data-bubble-id={element.id}
        data-bubble-type={element.dialogueStyle.type}
        data-bubble-mode={mode}
        className={getBubbleClasses()}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleBubbleClick}
        onDoubleClick={handleBubbleDoubleClick}
        style={{
          width: '100%',
          height: '100%',
          position: 'relative',
          overflow: 'hidden',
          cursor: mode === 'editing' ? 'text' : 'pointer'
        }}
      >
        {/* ✅ RENDU CONDITIONNEL SELON LE MODE */}
        {mode === 'editing' && editor ? (
          <EditorContent
            ref={editorRef}
            editor={editor}
            className="tiptap-bubble-editor w-full h-full flex items-center justify-center outline-none p-2"
            style={{
              ...textStyle,
              maxWidth: '100%',
              maxHeight: '100%',
              wordWrap: 'break-word',
              overflowWrap: 'break-word'
            }}
          />
        ) : (
          <div
            className="tiptap-bubble-text w-full h-full flex items-center justify-center p-2"
            style={{
              ...textStyle,
              maxWidth: '100%',
              maxHeight: '100%',
              wordWrap: 'break-word',
              overflowWrap: 'break-word'
            }}
            dangerouslySetInnerHTML={{
              __html: element.text || 'Double-cliquez pour éditer...'
            }}
          />
        )}
      </div>
    </div>
  )
}
