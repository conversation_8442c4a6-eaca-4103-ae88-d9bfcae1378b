'use client'

// TipTapBubble - Système de speech bubbles avec manipulation complète
// Architecture: TipTap + système de manipulation identique aux panels

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import { DialogueElement, BubbleType } from '../types/assembly.types'
// ✅ SUPPRIMÉ : TipTapBubbleManipulator - manipulation gérée par SimpleCanvasEditor
import BubbleQueue from './BubbleQueue'

// Types pour les modes UX
export type BubbleMode = 'reading' | 'editing' | 'manipulating'

interface TipTapBubbleProps {
  element: DialogueElement
  isSelected: boolean
  mode: BubbleMode
  onUpdate: (bubbleId: string, updates: Partial<DialogueElement>) => void
  onDoubleClick?: (bubbleId: string) => void
  onModeChange?: (bubbleId: string, newMode: BubbleMode) => void
  // ✅ NOUVEAU : onModeChange pour gérer les transitions de modes
}

/**
 * Nouveau système de speech bubbles basé sur TipTap
 * Trois modes UX distincts : lecture, édition, manipulation
 */
export default function TipTapBubble({
  element,
  isSelected,
  mode,
  onUpdate,
  onDoubleClick,
  onModeChange
}: TipTapBubbleProps) {
  const bubbleRef = useRef<HTMLDivElement>(null)
  const editorRef = useRef<any>(null)

  // États locaux
  const [isHovered, setIsHovered] = useState(false)
  const [contentSize, setContentSize] = useState({ width: 0, height: 0 })

  // ✅ TIPTAP COMME CŒUR DU SYSTÈME
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Configuration optimisée pour les speech bubbles
        heading: false,
        blockquote: false,
        codeBlock: false,
        horizontalRule: false,
        listItem: false,
        orderedList: false,
        bulletList: false,
      }),
      Placeholder.configure({
        placeholder: 'Tapez votre texte...',
        emptyEditorClass: 'is-editor-empty',
        emptyNodeClass: 'is-empty',
        showOnlyWhenEditable: true,
        showOnlyCurrent: true, // ✅ CORRIGÉ : Afficher seulement sur le nœud actuel
        includeChildren: false, // ✅ NOUVEAU : Ne pas inclure les enfants
      }),
    ],
    content: element.text || '',
    editable: mode === 'editing',
    immediatelyRender: false,
    shouldRerenderOnTransaction: false,
    editorProps: {
      attributes: {
        class: 'tiptap-bubble-editor',
        'data-placeholder': 'Tapez votre texte...', // ✅ PLACEHOLDER ATTRIBUT
        style: `
          outline: none;
          border: none;
          padding: 8px;
          margin: 0;
          width: 100%;
          height: 100%;
          color: #000000 !important;
          font-weight: 500;
          letter-spacing: 0.02em;
        `
      },
    },
    onUpdate: ({ editor }) => {
      const newText = editor.getHTML()
      console.log('📝 TipTap onUpdate:', element.id, 'New text:', newText)
      onUpdate(element.id, { text: newText })

      // Auto-redimensionnement selon le contenu
      measureContent()
    },
    onFocus: () => {
      console.log(`📝 TipTap focused for bubble: ${element.id}`)
    },
    onBlur: () => {
      console.log(`📝 TipTap blurred for bubble: ${element.id}`)
      if (mode === 'editing') {
        // Délai pour permettre le clic sur autre chose
        setTimeout(() => {
          onModeChange?.(element.id, 'reading')
          // Dispatcher l'événement pour SimpleCanvasEditor
          const modeChangeEvent = new CustomEvent('bubbleModeChangeFromBubble', {
            detail: { bubbleId: element.id, newMode: 'reading' }
          })
          window.dispatchEvent(modeChangeEvent)
        }, 100)
      }
    },
  })

  // ✅ AUTO-REDIMENSIONNEMENT SELON LE CONTENU (optimisé pour éviter le flash)
  const measureContent = useCallback(() => {
    if (!editor || !bubbleRef.current) return

    const textContent = editor.getText()

    // ✅ CORRECTION : Ne pas redimensionner si le texte est vide (nouvelle bulle)
    if (!textContent || textContent.trim() === '') {
      return
    }

    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!

    ctx.font = `${element.dialogueStyle.fontSize}px ${element.dialogueStyle.fontFamily}`
    const textMetrics = ctx.measureText(textContent)

    const padding = 16
    const minWidth = 100
    const minHeight = 60
    const maxWidth = 300

    const newWidth = Math.min(Math.max(textMetrics.width + padding * 2, minWidth), maxWidth)
    const newHeight = Math.max(element.dialogueStyle.fontSize * 1.5 + padding * 2, minHeight)

    setContentSize({ width: newWidth, height: newHeight })

    // ✅ CORRECTION : Seuil plus élevé pour éviter les micro-ajustements
    if (Math.abs(element.transform.width - newWidth) > 10 ||
        Math.abs(element.transform.height - newHeight) > 10) {
      onUpdate(element.id, {
        transform: {
          ...element.transform,
          width: newWidth,
          height: newHeight
        }
      })
    }
  }, [editor, element, onUpdate])

  // ✅ SYNCHRONISATION DU CONTENU TIPTAP
  useEffect(() => {
    if (editor && element.text !== editor.getHTML()) {
      // Mettre à jour le contenu de l'éditeur si element.text a changé
      editor.commands.setContent(element.text || '', false)
      console.log('🔄 TipTap content synchronized:', element.id, element.text)
    }
  }, [editor, element.text])

  // ✅ NOUVEAU : Forcer la mise à jour de l'éditabilité
  useEffect(() => {
    if (editor) {
      editor.setEditable(mode === 'editing')
      console.log('🔄 TipTap editable state updated:', element.id, mode === 'editing')
    }
  }, [editor, mode, element.id])

  // ✅ DEBUG : Vérifier l'état de l'éditeur
  useEffect(() => {
    if (editor) {
      console.log('🔍 TipTap editor state:', {
        id: element.id,
        mode,
        isEditable: editor.isEditable,
        isFocused: editor.isFocused,
        content: editor.getHTML()
      })
    }
  }, [editor, mode, element.id])

  // ✅ FOCUS AUTOMATIQUE EN MODE ÉDITION
  useEffect(() => {
    if (mode === 'editing' && editor) {
      // Focus sur l'éditeur quand on passe en mode édition
      setTimeout(() => {
        editor.commands.focus()
        console.log('🎯 TipTap focused for editing:', element.id)
      }, 100)
    }
  }, [mode, editor])

  // ✅ GESTION DES RACCOURCIS CLAVIER
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (mode === 'editing' && e.key === 'Escape') {
        e.preventDefault()
        console.log('🎯 TipTapBubble: Escape pressed, exiting edit mode')
        onModeChange?.(element.id, 'reading')
        // Dispatcher l'événement pour SimpleCanvasEditor
        const modeChangeEvent = new CustomEvent('bubbleModeChangeFromBubble', {
          detail: { bubbleId: element.id, newMode: 'reading' }
        })
        window.dispatchEvent(modeChangeEvent)
      }
    }

    if (mode === 'editing') {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [mode, element.id, onModeChange])

  // ✅ SUPPRIMÉ : Le positionnement est maintenant géré par TipTapBubbleManipulator

  // ✅ STYLES DE BULLE SELON LE TYPE (SANS ANIMATIONS)
  const getBubbleClasses = useCallback(() => {
    const baseClasses = 'tiptap-bubble flex items-center justify-center border-2'
    const modeClasses = {
      reading: '',
      editing: 'ring-2 ring-blue-500 ring-opacity-50',
      manipulating: 'ring-2 ring-green-500 ring-opacity-50'
    }
    // ✅ SUPPRIMÉ : hoverClasses - pas d'animations de survol

    // ✅ STYLES selon le type de bulle (SANS OMBRES/ANIMATIONS)
    const typeClasses = {
      speech: 'rounded-2xl border-gray-800 bg-white border-2',
      thought: 'rounded-full border-gray-600 bg-gray-50 border-dashed border-2',
      shout: 'rounded-lg border-red-700 bg-yellow-50 border-3',
      whisper: 'rounded-xl border-gray-500 bg-gray-100 border-2',
      explosion: 'rounded-lg border-orange-700 bg-yellow-50 border-3'
    }

    return `${baseClasses} ${modeClasses[mode]} ${typeClasses[element.dialogueStyle.type]}`
  }, [mode, element.dialogueStyle.type])

  // ✅ STYLES DE TEXTE - NOIR PUR FORCÉ POUR MAXIMUM CONTRASTE
  const textStyle = useMemo(() => {
    return {
      fontSize: `${element.dialogueStyle.fontSize}px`,
      fontFamily: element.dialogueStyle.fontFamily,
      color: '#000000', // ✅ FORCÉ : Noir pur pour maximum contraste
      textAlign: element.dialogueStyle.textAlign as 'left' | 'center' | 'right',
      lineHeight: 1.3,
      wordBreak: 'break-word' as const,
      fontWeight: '500',
      letterSpacing: '0.02em'
    }
  }, [element.dialogueStyle])

  // ✅ STYLES FORCÉS POUR CORRIGER LE PROBLÈME DE COULEUR
  const forceBlackTextStyle = {
    color: '#000000',
    caretColor: '#000000'
  }

  // ✅ NOUVEAU : Écouter les événements de changement de mode
  useEffect(() => {
    const handleModeChange = (event: CustomEvent) => {
      const { bubbleId, newMode } = event.detail
      if (bubbleId === element.id) {
        console.log('🎯 TipTapBubble: Mode change reçu:', bubbleId, newMode)
        onModeChange?.(bubbleId, newMode)

        // Si passage en mode édition, focus sur l'éditeur
        if (newMode === 'editing' && editor) {
          setTimeout(() => {
            editor.commands.focus()
            console.log('🎯 TipTapBubble: Focus sur éditeur:', bubbleId)
          }, 100)
        }
      }
    }

    window.addEventListener('bubbleModeChange', handleModeChange as EventListener)

    return () => {
      window.removeEventListener('bubbleModeChange', handleModeChange as EventListener)
    }
  }, [element.id, editor, onModeChange])

  // ✅ NOUVEAU : Enregistrer la bulle dans SimpleCanvasEditor pour la détection
  useEffect(() => {
    if (bubbleRef.current) {
      const bounds = bubbleRef.current.getBoundingClientRect()
      const registerEvent = new CustomEvent('registerTipTapBubble', {
        detail: {
          bubbleId: element.id,
          element: bubbleRef.current,
          bounds: bounds
        }
      })
      window.dispatchEvent(registerEvent)
      console.log('📝 TipTapBubble enregistré:', element.id)

      // Cleanup au démontage
      return () => {
        const unregisterEvent = new CustomEvent('unregisterTipTapBubble', {
          detail: { bubbleId: element.id }
        })
        window.dispatchEvent(unregisterEvent)
        console.log('🗑️ TipTapBubble désenregistré:', element.id)
      }
    }
  }, [element.id])

  // ✅ NOUVEAU : Mettre à jour les bounds quand la position change
  useEffect(() => {
    if (bubbleRef.current) {
      const bounds = bubbleRef.current.getBoundingClientRect()
      const updateEvent = new CustomEvent('updateTipTapBubbleBounds', {
        detail: {
          bubbleId: element.id,
          bounds: bounds
        }
      })
      window.dispatchEvent(updateEvent)
    }
  }, [element.transform.x, element.transform.y, element.transform.width, element.transform.height])

  // ✅ CORRECTION : Mesurer le contenu seulement quand il y a du texte
  useEffect(() => {
    // Ne pas mesurer immédiatement au montage pour éviter le flash
    if (element.text && element.text.trim() !== '') {
      // Délai court pour laisser le temps au DOM de se stabiliser
      const timeoutId = setTimeout(() => {
        measureContent()
      }, 50)

      return () => clearTimeout(timeoutId)
    }
  }, [measureContent, element.text])

  // ✅ NOUVEAU : Drag immédiat comme les panels
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0, elementX: 0, elementY: 0 })

  const handleBubbleMouseDown = useCallback((event: React.MouseEvent) => {
    if (mode === 'editing') return // Pas de drag en mode édition

    event.preventDefault()
    event.stopPropagation()

    setIsDragging(true)
    setDragStart({
      x: event.clientX,
      y: event.clientY,
      elementX: element.transform.x,
      elementY: element.transform.y
    })

    console.log('🎯 TipTapBubble: Drag immédiat start:', element.id)

    // Sélectionner la bulle
    const bubbleClickEvent = new CustomEvent('bubbleClicked', {
      detail: {
        bubbleId: element.id,
        clientX: event.clientX,
        clientY: event.clientY,
        element: bubbleRef.current
      }
    })
    window.dispatchEvent(bubbleClickEvent)
  }, [mode, element.id, element.transform.x, element.transform.y])

  const handleBubbleDoubleClick = useCallback((event: React.MouseEvent) => {
    if (mode !== 'reading') return

    event.stopPropagation()
    console.log('🎯 TipTapBubble: Double-click pour édition:', element.id)
    onDoubleClick?.(element.id)
  }, [mode, element.id, onDoubleClick])

  // ✅ GESTION DU DRAG GLOBAL
  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      if (!isDragging) return

      const deltaX = event.clientX - dragStart.x
      const deltaY = event.clientY - dragStart.y

      const newX = dragStart.elementX + deltaX
      const newY = dragStart.elementY + deltaY

      onUpdate(element.id, {
        transform: {
          ...element.transform,
          x: newX,
          y: newY
        }
      })
    }

    const handleMouseUp = () => {
      if (isDragging) {
        setIsDragging(false)
        console.log('🎯 TipTapBubble: Drag immédiat end:', element.id)
      }
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)

      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, dragStart, element.id, element.transform, onUpdate])

  // ✅ FORCER LES STYLES CSS GLOBAUX POUR LA COULEUR
  useEffect(() => {
    const styleId = 'force-bubble-text-color'
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style')
      style.id = styleId
      style.textContent = `
        /* ✅ FORCER LA COULEUR NOIRE PARTOUT */
        .tiptap-bubble-text, .tiptap-bubble-text *,
        .tiptap-bubble-text p, .tiptap-bubble-text span,
        .tiptap-bubble-editor, .tiptap-bubble-editor *,
        .ProseMirror, .ProseMirror *, .ProseMirror p {
          color: #000000 !important;
          caret-color: #000000 !important;
        }

        /* ✅ OVERRIDE TAILWIND ET AUTRES CSS */
        [data-bubble-id] * {
          color: #000000 !important;
        }

        /* ✅ CURSEURS PAR MODE */
        .bubble-reading-mode { cursor: pointer !important; }
        .bubble-manipulation-mode { cursor: grab !important; }
        .bubble-dragging { cursor: grabbing !important; }
        .bubble-editing-mode { cursor: text !important; }
      `
      document.head.appendChild(style)
    }
  }, [])

  return (
    <>
      {/* ✅ SUPPRIMÉ : styled-jsx qui causait des problèmes de couleur */}

      <div
        style={{
          position: 'absolute',
          left: element.transform.x,
          top: element.transform.y,
          width: element.transform.width,
          height: element.transform.height,
          zIndex: 3000, // ✅ FORCÉ : Bulles toujours au-dessus de tout (panels ~100-500)
          pointerEvents: 'auto' // ✅ PERMETTRE LES CLICS
        }}
      >
      {/* Queue de la bulle */}
      <BubbleQueue element={element} />

      {/* Contenu de la bulle */}
      <div
        ref={bubbleRef}
        data-bubble-id={element.id}
        data-bubble-type={element.dialogueStyle.type}
        data-bubble-mode={mode}
        className={`${getBubbleClasses()} bubble-${mode}-mode ${isDragging ? 'bubble-dragging' : ''}`}
        // ✅ SUPPRIMÉ : onMouseEnter/Leave - pas d'animations de survol
        onMouseDown={handleBubbleMouseDown}
        onDoubleClick={handleBubbleDoubleClick}
        style={{
          width: '100%',
          height: '100%',
          position: 'relative',
          overflow: 'hidden'
          // ✅ CURSOR géré par les classes CSS
        }}
      >
        {/* ✅ RENDU CONDITIONNEL SELON LE MODE */}
        {mode === 'editing' && editor ? (
          <EditorContent
            ref={editorRef}
            editor={editor}
            className="tiptap-bubble-editor w-full h-full flex items-center justify-center outline-none p-2"
            style={{
              ...textStyle,
              ...forceBlackTextStyle, // ✅ FORCÉ : Styles noirs
              maxWidth: '100%',
              maxHeight: '100%',
              wordWrap: 'break-word',
              overflowWrap: 'break-word'
            }}
          />
        ) : (
          <div
            className="tiptap-bubble-text w-full h-full flex items-center justify-center p-2"
            style={{
              ...textStyle,
              ...forceBlackTextStyle, // ✅ FORCÉ : Styles noirs
              maxWidth: '100%',
              maxHeight: '100%',
              wordWrap: 'break-word',
              overflowWrap: 'break-word'
            }}
            dangerouslySetInnerHTML={{
              __html: `<span style="color: #000000 !important;">${element.text || 'Double-cliquez pour éditer...'}</span>`
            }}
          />
        )}
      </div>
    </div>
    </>
  )
}
