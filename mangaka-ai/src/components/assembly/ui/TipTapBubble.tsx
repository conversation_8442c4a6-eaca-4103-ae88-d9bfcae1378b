'use client'

// TipTapBubble - Système de speech bubbles avec manipulation complète
// Architecture: TipTap + système de manipulation identique aux panels

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import { DialogueElement, BubbleType } from '../types/assembly.types'
import TipTapBubbleManipulator from './TipTapBubbleManipulator'
import BubbleQueue from './BubbleQueue'

// Types pour les modes UX
export type BubbleMode = 'reading' | 'editing' | 'manipulating'

interface TipTapBubbleProps {
  element: DialogueElement
  isSelected: boolean
  mode: BubbleMode
  onSelect: (bubbleId: string) => void
  onModeChange: (bubbleId: string, mode: BubbleMode) => void
  onUpdate: (bubbleId: string, updates: Partial<DialogueElement>) => void
  onDoubleClick?: (bubbleId: string) => void
}

/**
 * Nouveau système de speech bubbles basé sur TipTap
 * Trois modes UX distincts : lecture, édition, manipulation
 */
export default function TipTapBubble({
  element,
  isSelected,
  mode,
  onSelect,
  onModeChange,
  onUpdate,
  onDoubleClick
}: TipTapBubbleProps) {
  const bubbleRef = useRef<HTMLDivElement>(null)
  const editorRef = useRef<any>(null)

  // États locaux
  const [isHovered, setIsHovered] = useState(false)
  const [contentSize, setContentSize] = useState({ width: 0, height: 0 })

  // ✅ TIPTAP COMME CŒUR DU SYSTÈME
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Configuration optimisée pour les speech bubbles
        heading: false,
        blockquote: false,
        codeBlock: false,
        horizontalRule: false,
        listItem: false,
        orderedList: false,
        bulletList: false,
      }),
      Placeholder.configure({
        placeholder: 'Tapez votre texte...',
      }),
    ],
    content: element.text || '',
    editable: mode === 'editing',
    immediatelyRender: false,
    shouldRerenderOnTransaction: false,
    editorProps: {
      attributes: {
        class: 'tiptap-bubble-editor',
        style: 'outline: none; border: none; padding: 8px; margin: 0; width: 100%; height: 100%;'
      },
    },
    onUpdate: ({ editor }) => {
      const newText = editor.getHTML()
      onUpdate(element.id, { text: newText })
      
      // Auto-redimensionnement selon le contenu
      measureContent()
    },
    onFocus: () => {
      console.log(`📝 TipTap focused for bubble: ${element.id}`)
    },
    onBlur: () => {
      console.log(`📝 TipTap blurred for bubble: ${element.id}`)
      if (mode === 'editing') {
        onModeChange(element.id, 'reading')
      }
    },
  })

  // ✅ AUTO-REDIMENSIONNEMENT SELON LE CONTENU (optimisé pour éviter le flash)
  const measureContent = useCallback(() => {
    if (!editor || !bubbleRef.current) return

    const textContent = editor.getText()

    // ✅ CORRECTION : Ne pas redimensionner si le texte est vide (nouvelle bulle)
    if (!textContent || textContent.trim() === '') {
      return
    }

    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!

    ctx.font = `${element.dialogueStyle.fontSize}px ${element.dialogueStyle.fontFamily}`
    const textMetrics = ctx.measureText(textContent)

    const padding = 16
    const minWidth = 100
    const minHeight = 60
    const maxWidth = 300

    const newWidth = Math.min(Math.max(textMetrics.width + padding * 2, minWidth), maxWidth)
    const newHeight = Math.max(element.dialogueStyle.fontSize * 1.5 + padding * 2, minHeight)

    setContentSize({ width: newWidth, height: newHeight })

    // ✅ CORRECTION : Seuil plus élevé pour éviter les micro-ajustements
    if (Math.abs(element.transform.width - newWidth) > 10 ||
        Math.abs(element.transform.height - newHeight) > 10) {
      onUpdate(element.id, {
        transform: {
          ...element.transform,
          width: newWidth,
          height: newHeight
        }
      })
    }
  }, [editor, element, onUpdate])

  // ✅ FOCUS AUTOMATIQUE EN MODE ÉDITION
  useEffect(() => {
    if (mode === 'editing' && editor) {
      // Focus sur l'éditeur quand on passe en mode édition
      setTimeout(() => {
        editor.commands.focus()
      }, 100)
    }
  }, [mode, editor])

  // ✅ GESTION DES RACCOURCIS CLAVIER
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (mode === 'editing' && e.key === 'Escape') {
        e.preventDefault()
        onModeChange(element.id, 'reading')
      }
    }

    if (mode === 'editing') {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [mode, element.id, onModeChange])

  // ✅ SUPPRIMÉ : Le positionnement est maintenant géré par TipTapBubbleManipulator

  // ✅ STYLES DE BULLE SELON LE TYPE
  const getBubbleClasses = useCallback(() => {
    const baseClasses = 'tiptap-bubble flex items-center justify-center border-2 transition-all duration-200'
    const modeClasses = {
      reading: '',
      editing: 'ring-2 ring-blue-500 ring-opacity-50',
      manipulating: 'ring-2 ring-green-500 ring-opacity-50'
    }
    const hoverClasses = isHovered && mode !== 'editing' ? 'shadow-lg' : ''

    // Styles selon le type de bulle
    const typeClasses = {
      speech: 'rounded-2xl border-gray-800 bg-white shadow-md',
      thought: 'rounded-full border-gray-500 bg-white border-dashed shadow-sm',
      shout: 'rounded-lg border-red-600 bg-yellow-50 shadow-lg border-4',
      whisper: 'rounded-xl border-gray-400 bg-gray-50 shadow-sm',
      explosion: 'rounded-lg border-orange-600 bg-yellow-100 shadow-lg'
    }

    return `${baseClasses} ${modeClasses[mode]} ${hoverClasses} ${typeClasses[element.dialogueStyle.type]}`
  }, [mode, isHovered, element.dialogueStyle.type])

  // ✅ STYLES DE TEXTE
  const textStyle = useMemo(() => ({
    fontSize: `${element.dialogueStyle.fontSize}px`,
    fontFamily: element.dialogueStyle.fontFamily,
    color: typeof element.dialogueStyle.textColor === 'string' 
      ? element.dialogueStyle.textColor 
      : `#${element.dialogueStyle.textColor.toString(16).padStart(6, '0')}`,
    textAlign: element.dialogueStyle.textAlign as 'left' | 'center' | 'right',
    lineHeight: 1.2,
    wordBreak: 'break-word' as const
  }), [element.dialogueStyle])

  // ✅ CORRECTION : Mesurer le contenu seulement quand il y a du texte
  useEffect(() => {
    // Ne pas mesurer immédiatement au montage pour éviter le flash
    if (element.text && element.text.trim() !== '') {
      // Délai court pour laisser le temps au DOM de se stabiliser
      const timeoutId = setTimeout(() => {
        measureContent()
      }, 50)

      return () => clearTimeout(timeoutId)
    }
  }, [measureContent, element.text])

  return (
    <TipTapBubbleManipulator
      element={element}
      isSelected={isSelected}
      mode={mode}
      onUpdate={onUpdate}
      onModeChange={onModeChange}
      onDoubleClick={onDoubleClick}
      onSelect={onSelect}
    >
      {/* Queue de la bulle */}
      <BubbleQueue element={element} />

      {/* Contenu de la bulle */}
      <div
        ref={bubbleRef}
        data-bubble-id={element.id}
        data-bubble-type={element.dialogueStyle.type}
        data-bubble-mode={mode}
        className={getBubbleClasses()}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{
          width: '100%',
          height: '100%',
          position: 'relative',
          overflow: 'hidden' // ✅ EMPÊCHER LE DÉBORDEMENT DU TEXTE
        }}
      >
        {/* ✅ RENDU CONDITIONNEL SELON LE MODE */}
        {mode === 'editing' && editor ? (
          <EditorContent
            ref={editorRef}
            editor={editor}
            className="tiptap-bubble-editor w-full h-full flex items-center justify-center outline-none p-2"
            style={{
              ...textStyle,
              maxWidth: '100%',
              maxHeight: '100%',
              wordWrap: 'break-word',
              overflowWrap: 'break-word'
            }}
          />
        ) : (
          <div
            className="tiptap-bubble-text w-full h-full flex items-center justify-center p-2"
            style={{
              ...textStyle,
              maxWidth: '100%',
              maxHeight: '100%',
              wordWrap: 'break-word',
              overflowWrap: 'break-word'
            }}
            dangerouslySetInnerHTML={{
              __html: element.text || 'Double-cliquez pour éditer...'
            }}
          />
        )}
      </div>
    </TipTapBubbleManipulator>
  )
}
