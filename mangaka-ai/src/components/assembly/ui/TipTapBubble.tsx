'use client'

// TipTapBubble - Nouveau système de speech bubbles "TipTap-first"
// Architecture: TipTap comme cœur, entouré d'une bulle graphique et système de queue

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import { DialogueElement, BubbleType } from '../types/assembly.types'
import { UnifiedCoordinateSystem } from '../core/CoordinateSystem'
import { LayerManager } from '../core/LayerManager'

// Types pour les modes UX
export type BubbleMode = 'reading' | 'editing' | 'manipulating'

interface TipTapBubbleProps {
  element: DialogueElement
  coordinateSystem: UnifiedCoordinateSystem
  isSelected: boolean
  mode: BubbleMode
  onSelect: (bubbleId: string) => void
  onModeChange: (bubbleId: string, mode: BubbleMode) => void
  onUpdate: (bubbleId: string, updates: Partial<DialogueElement>) => void
  onStartManipulation?: (element: DialogueElement, handleType: string, globalX: number, globalY: number) => void
}

/**
 * Nouveau système de speech bubbles basé sur TipTap
 * Trois modes UX distincts : lecture, édition, manipulation
 */
export default function TipTapBubble({
  element,
  coordinateSystem,
  isSelected,
  mode,
  onSelect,
  onModeChange,
  onUpdate,
  onStartManipulation
}: TipTapBubbleProps) {
  const bubbleRef = useRef<HTMLDivElement>(null)
  const editorRef = useRef<any>(null)
  const layerManager = LayerManager.getInstance()
  
  // États locaux
  const [isHovered, setIsHovered] = useState(false)
  const [contentSize, setContentSize] = useState({ width: 0, height: 0 })

  // ✅ TIPTAP COMME CŒUR DU SYSTÈME
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Configuration optimisée pour les speech bubbles
        heading: false,
        blockquote: false,
        codeBlock: false,
        horizontalRule: false,
        listItem: false,
        orderedList: false,
        bulletList: false,
      }),
      Placeholder.configure({
        placeholder: 'Tapez votre texte...',
      }),
    ],
    content: element.text || '',
    editable: mode === 'editing',
    immediatelyRender: false,
    shouldRerenderOnTransaction: false,
    editorProps: {
      attributes: {
        class: 'tiptap-bubble-editor',
        style: 'outline: none; border: none; padding: 8px; margin: 0; width: 100%; height: 100%;'
      },
    },
    onUpdate: ({ editor }) => {
      const newText = editor.getHTML()
      onUpdate(element.id, { text: newText })
      
      // Auto-redimensionnement selon le contenu
      measureContent()
    },
    onFocus: () => {
      console.log(`📝 TipTap focused for bubble: ${element.id}`)
    },
    onBlur: () => {
      console.log(`📝 TipTap blurred for bubble: ${element.id}`)
      if (mode === 'editing') {
        onModeChange(element.id, 'reading')
      }
    },
  })

  // ✅ AUTO-REDIMENSIONNEMENT SELON LE CONTENU
  const measureContent = useCallback(() => {
    if (!editor || !bubbleRef.current) return

    const textContent = editor.getText()
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    ctx.font = `${element.dialogueStyle.fontSize}px ${element.dialogueStyle.fontFamily}`
    const textMetrics = ctx.measureText(textContent)
    
    const padding = 16
    const minWidth = 100
    const minHeight = 60
    const maxWidth = 300
    
    const newWidth = Math.min(Math.max(textMetrics.width + padding * 2, minWidth), maxWidth)
    const newHeight = Math.max(element.dialogueStyle.fontSize * 1.5 + padding * 2, minHeight)
    
    setContentSize({ width: newWidth, height: newHeight })
    
    // Mettre à jour les dimensions de l'élément si nécessaire
    if (Math.abs(element.transform.width - newWidth) > 5 || 
        Math.abs(element.transform.height - newHeight) > 5) {
      onUpdate(element.id, {
        transform: {
          ...element.transform,
          width: newWidth,
          height: newHeight
        }
      })
    }
  }, [editor, element, onUpdate])

  // ✅ GESTION DES ÉVÉNEMENTS SELON LE MODE
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()
    
    if (mode === 'reading') {
      onSelect(element.id)
      onModeChange(element.id, 'manipulating')
    }
  }, [mode, element.id, onSelect, onModeChange])

  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()
    
    if (mode !== 'editing') {
      onModeChange(element.id, 'editing')
      // Focus sur l'éditeur après un court délai
      setTimeout(() => {
        if (editor) {
          editor.commands.focus()
        }
      }, 50)
    }
  }, [mode, element.id, onModeChange, editor])

  // ✅ GESTION DES RACCOURCIS CLAVIER
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (mode === 'editing' && e.key === 'Escape') {
        e.preventDefault()
        onModeChange(element.id, 'reading')
      }
    }

    if (mode === 'editing') {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [mode, element.id, onModeChange])

  // ✅ POSITIONNEMENT ET STYLES
  const bubbleStyle = useMemo(() => {
    // Z-index basé sur la sélection et le mode
    const baseZIndex = 1000
    const zIndex = isSelected
      ? baseZIndex + 100 + (mode === 'editing' ? 50 : mode === 'manipulating' ? 30 : 0)
      : baseZIndex + element.transform.zIndex

    return {
      position: 'absolute' as const,
      left: `${element.transform.x}px`,
      top: `${element.transform.y}px`,
      width: `${element.transform.width}px`,
      height: `${element.transform.height}px`,
      zIndex,
      pointerEvents: 'auto' as const,
      cursor: mode === 'editing' ? 'text' : mode === 'manipulating' ? 'move' : 'pointer',
      transform: `rotate(${element.transform.rotation}deg)`,
      opacity: element.transform.alpha,
      transition: mode === 'editing' ? 'none' : 'all 0.2s ease-out'
    }
  }, [element.transform, isSelected, mode])

  // ✅ STYLES DE BULLE SELON LE TYPE
  const getBubbleClasses = useCallback(() => {
    const baseClasses = 'tiptap-bubble flex items-center justify-center border-2 transition-all duration-200'
    const modeClasses = {
      reading: '',
      editing: 'ring-2 ring-blue-500 ring-opacity-50',
      manipulating: 'ring-2 ring-green-500 ring-opacity-50'
    }
    const hoverClasses = isHovered && mode !== 'editing' ? 'shadow-lg' : ''

    // Styles selon le type de bulle
    const typeClasses = {
      speech: 'rounded-2xl border-gray-800 bg-white shadow-md',
      thought: 'rounded-full border-gray-500 bg-white border-dashed shadow-sm',
      shout: 'rounded-lg border-red-600 bg-yellow-50 shadow-lg border-4',
      whisper: 'rounded-xl border-gray-400 bg-gray-50 shadow-sm',
      explosion: 'rounded-lg border-orange-600 bg-yellow-100 shadow-lg'
    }

    return `${baseClasses} ${modeClasses[mode]} ${hoverClasses} ${typeClasses[element.dialogueStyle.type]}`
  }, [mode, isHovered, element.dialogueStyle.type])

  // ✅ STYLES DE TEXTE
  const textStyle = useMemo(() => ({
    fontSize: `${element.dialogueStyle.fontSize}px`,
    fontFamily: element.dialogueStyle.fontFamily,
    color: typeof element.dialogueStyle.textColor === 'string' 
      ? element.dialogueStyle.textColor 
      : `#${element.dialogueStyle.textColor.toString(16).padStart(6, '0')}`,
    textAlign: element.dialogueStyle.textAlign as 'left' | 'center' | 'right',
    lineHeight: 1.2,
    wordBreak: 'break-word' as const
  }), [element.dialogueStyle])

  // Mesurer le contenu au montage et lors des changements
  useEffect(() => {
    measureContent()
  }, [measureContent, element.text])

  return (
    <div
      ref={bubbleRef}
      data-bubble-id={element.id}
      data-bubble-type={element.dialogueStyle.type}
      data-bubble-mode={mode}
      style={bubbleStyle}
      className={getBubbleClasses()}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* ✅ RENDU CONDITIONNEL SELON LE MODE */}
      {mode === 'editing' && editor ? (
        <EditorContent 
          ref={editorRef}
          editor={editor}
          className="tiptap-bubble-editor w-full h-full flex items-center justify-center outline-none"
          style={textStyle}
        />
      ) : (
        <div 
          className="tiptap-bubble-text w-full h-full flex items-center justify-center"
          style={textStyle}
          dangerouslySetInnerHTML={{ 
            __html: element.text || 'Double-cliquez pour éditer...' 
          }}
        />
      )}
    </div>
  )
}
