'use client'

// TipTapBubbleLayer - Couche HTML pour les nouvelles bulles TipTap
// Intégration avec le système de coordonnées unifié et gestionnaire de modes

import React, { useEffect, useRef, useMemo, useState } from 'react'
import { CanvasTransform, ViewportInfo } from '../core/CoordinateSystem'
import { LayerManager } from '../core/LayerManager'
import { useTipTapBubbleContext } from '../context/TipTapBubbleContext'
import TipTapBubble, { BubbleMode } from './TipTapBubble'
import BubbleQueue from './BubbleQueue'
import { DialogueElement } from '../types/assembly.types'
import './TipTapBubble.css'

interface TipTapBubbleLayerProps {
  canvasTransform: CanvasTransform
  canvasSize: { width: number; height: number }
  viewport: ViewportInfo
  className?: string
}

/**
 * Couche HTML pour les bulles TipTap
 * Gère le positionnement, la synchronisation et les modes UX
 */
export default function TipTapBubbleLayer({
  canvasTransform,
  canvasSize,
  viewport,
  className = ''
}: TipTapBubbleLayerProps) {
  const {
    bubbles,
    selectedBubbleIds,
    createBubble,
    updateBubble,
    selectBubble,
    setBubbleMode,
    getBubbleMode
  } = useTipTapBubbleContext()

  const layerRef = useRef<HTMLDivElement>(null)
  const layerManager = LayerManager.getInstance()

  // ✅ ÉCOUTER LES ÉVÉNEMENTS DE CRÉATION DE BULLES
  useEffect(() => {
    const handleCreateBubble = (event: CustomEvent) => {
      const { x, y, bubbleType } = event.detail
      console.log('🎯 TipTapBubbleLayer: Réception événement création bulle', { x, y, bubbleType })

      // ✅ CRÉER LA BULLE VIA LE CONTEXTE
      const newBubble = createBubble(x, y, bubbleType)
      console.log('✅ TipTapBubbleLayer: Bulle TipTap créée:', newBubble.id)
    }

    window.addEventListener('createTipTapBubble', handleCreateBubble as EventListener)
    return () => window.removeEventListener('createTipTapBubble', handleCreateBubble as EventListener)
  }, [createBubble])

  // ✅ GESTION DES MODES UX
  const handleModeChange = (bubbleId: string, mode: BubbleMode) => {
    setBubbleMode(bubbleId, mode)

    // Si on passe en mode édition, s'assurer que la bulle est sélectionnée
    if (mode === 'editing') {
      selectBubble(bubbleId)
    }
  }

  // ✅ GESTION DES ÉVÉNEMENTS DE COUCHE
  const handleLayerClick = (e: React.MouseEvent) => {
    // Les bulles TipTap sont créées via l'événement personnalisé depuis SimpleCanvasEditor
    // Cette fonction est gardée pour d'éventuelles futures fonctionnalités
    console.log('🎯 TipTapBubbleLayer: Clic sur la couche')
  }

  // ✅ GESTION DE LA MANIPULATION
  const handleStartManipulation = (
    element: DialogueElement, 
    handleType: string, 
    globalX: number, 
    globalY: number
  ) => {
    console.log('🎯 TipTapBubbleLayer: Début manipulation', {
      elementId: element.id,
      handleType,
      position: { globalX, globalY }
    })
    
    // Passer en mode manipulation
    handleModeChange(element.id, 'manipulating')
  }

  // ✅ STYLES DE LA COUCHE
  const layerStyle = useMemo(() => ({
    position: 'absolute' as const,
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    pointerEvents: 'auto' as const, // Toujours actif pour les bulles TipTap
    zIndex: 1000, // Z-index fixe pour la couche des bulles TipTap
    overflow: 'hidden'
  }), [])

  // ✅ SYNCHRONISATION AVEC LE VIEWPORT
  useEffect(() => {
    if (!layerRef.current) return

    const layer = layerRef.current
    layer.style.transform = `translate(${canvasTransform.x}px, ${canvasTransform.y}px) scale(${canvasTransform.scale})`
    layer.style.transformOrigin = '0 0'

    console.log('🔄 TipTapBubbleLayer: Synchronisation viewport', {
      transform: canvasTransform,
      bubblesCount: bubbles.length
    })
  }, [canvasTransform, bubbles.length])

  return (
    <div
      ref={layerRef}
      className={`tiptap-bubble-layer ${className}`}
      style={layerStyle}
      onClick={handleLayerClick}
    >
      {bubbles.map(bubble => {
        const isSelected = selectedBubbleIds.includes(bubble.id)
        const mode = getBubbleMode(bubble.id)
        
        return (
          <div key={bubble.id} className="tiptap-bubble-container relative">
            {/* Queue de la bulle */}
            <BubbleQueue element={bubble} />
            
            {/* Bulle principale */}
            <TipTapBubble
              element={bubble}
              coordinateSystem={{} as any} // TODO: Passer le vrai système de coordonnées
              isSelected={isSelected}
              mode={mode}
              onSelect={selectBubble}
              onModeChange={handleModeChange}
              onUpdate={updateBubble}
              onStartManipulation={handleStartManipulation}
            />
            
            {/* Handles de manipulation en mode manipulating */}
            {mode === 'manipulating' && isSelected && (
              <div className="tiptap-bubble-handles">
                <div className="tiptap-bubble-handle top-left" />
                <div className="tiptap-bubble-handle top-right" />
                <div className="tiptap-bubble-handle bottom-left" />
                <div className="tiptap-bubble-handle bottom-right" />
              </div>
            )}
          </div>
        )
      })}
    </div>
  )
}
