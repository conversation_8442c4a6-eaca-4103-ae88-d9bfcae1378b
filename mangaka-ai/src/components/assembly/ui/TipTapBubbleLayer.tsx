'use client'

// TipTapBubbleLayer - Couche HTML pour les nouvelles bulles TipTap
// Intégration avec le système de coordonnées unifié et gestionnaire de modes

import React, { useEffect, useRef, useMemo, useState, useCallback } from 'react'
import { CanvasTransform, ViewportInfo } from '../core/CoordinateSystem'
import { LayerManager } from '../core/LayerManager'
import { useCanvasContext } from '../context/CanvasContext'
import TipTapBubble, { BubbleMode } from './TipTapBubble'
import { DialogueElement } from '../types/assembly.types'
import './TipTapBubble.css'

interface TipTapBubbleLayerProps {
  canvasTransform: CanvasTransform
  canvasSize: { width: number; height: number }
  viewport: ViewportInfo
  className?: string
}

/**
 * Couche HTML pour les bulles TipTap
 * Gère le positionnement, la synchronisation et les modes UX
 */
export default function TipTapBubbleLayer({
  canvasTransform,
  canvasSize,
  viewport,
  className = ''
}: TipTapBubbleLayerProps) {
  const {
    elements,
    selectedElementIds,
    addElement,
    updateElement,
    selectElement,
    activeTool,
    setActiveTool
  } = useCanvasContext()

  const layerRef = useRef<HTMLDivElement>(null)
  const layerManager = LayerManager.getInstance()

  // États locaux pour l'édition
  const [editingBubbleId, setEditingBubbleId] = useState<string | null>(null)

  // ✅ FILTRER LES BULLES DIALOGUE
  const bubbles = useMemo(() => {
    return elements.filter((element): element is DialogueElement =>
      element.type === 'dialogue'
    )
  }, [elements])

  // ✅ ÉCOUTER LES ÉVÉNEMENTS DE CRÉATION DE BULLES
  useEffect(() => {
    const handleCreateBubble = (event: CustomEvent) => {
      const { x, y, bubbleType } = event.detail
      console.log('🎯 TipTapBubbleLayer: Réception événement création bulle', { x, y, bubbleType })

      // ✅ DEBUG : Vérifier les dimensions et positions des conteneurs
      if (layerRef.current) {
        const layerRect = layerRef.current.getBoundingClientRect()
        const parentRect = layerRef.current.parentElement?.getBoundingClientRect()
        console.log('🔍 DEBUG TipTapBubbleLayer:', {
          layerRect: { x: layerRect.x, y: layerRect.y, width: layerRect.width, height: layerRect.height },
          parentRect: parentRect ? { x: parentRect.x, y: parentRect.y, width: parentRect.width, height: parentRect.height } : null,
          canvasTransform,
          receivedCoords: { x, y }
        })
      }

      // ✅ CRÉER LA BULLE VIA LE CONTEXTE CANVAS
      const bubble: DialogueElement = {
        id: `bubble_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: 'dialogue',
        layerType: 'dialogue',
        text: '',
        transform: {
          x: x - 75, // Centrer la bulle
          y: y - 40,
          rotation: 0,
          alpha: 1,
          zIndex: 200,
          width: 150,
          height: 80
        },
        dialogueStyle: {
          type: bubbleType as any,
          backgroundColor: 0xffffff,
          outlineColor: 0x000000,
          outlineWidth: 2,
          textColor: 0x000000,
          fontSize: 16,
          fontFamily: 'Arial, sans-serif',
          textAlign: 'center',
          dashedOutline: false,
          tailPosition: 'bottom-left',
          tailLength: 30,
          tailAngleDegrees: 225,
          tailAttachmentSide: 'bottom'
        },
        properties: {
          visible: true,
          locked: false,
          selectable: true,
          name: `Bulle ${bubbleType}`
        }
      }

      // Ajouter la bulle au contexte
      addElement(bubble)

      // Switch vers select tool et sélectionner la bulle
      setActiveTool('select')
      selectElement(bubble.id)

      console.log('✅ Bulle créée et sélectionnée:', bubble.id)
    }

    window.addEventListener('createTipTapBubble', handleCreateBubble as EventListener)
    return () => window.removeEventListener('createTipTapBubble', handleCreateBubble as EventListener)
  }, [addElement, setActiveTool, selectElement])

  // ✅ GESTION DES MODES UX
  const getBubbleMode = useCallback((bubbleId: string): BubbleMode => {
    if (editingBubbleId === bubbleId) return 'editing'
    if (selectedElementIds.includes(bubbleId)) return 'manipulating'
    return 'reading'
  }, [editingBubbleId, selectedElementIds])

  const handleModeChange = useCallback((bubbleId: string, mode: BubbleMode) => {
    console.log('🔄 Mode change:', bubbleId, mode)

    if (mode === 'editing') {
      setEditingBubbleId(bubbleId)
      selectElement(bubbleId) // S'assurer que la bulle est sélectionnée
    } else if (mode === 'reading') {
      setEditingBubbleId(null)
      selectElement(null) // Désélectionner
    } else if (mode === 'manipulating') {
      setEditingBubbleId(null)
      selectElement(bubbleId) // Sélectionner pour manipulation
    }
  }, [selectElement])

  // ✅ GESTION DU DOUBLE-CLIC POUR ÉDITION
  const handleBubbleDoubleClick = useCallback((bubbleId: string) => {
    console.log('🎨 Double-clic sur bulle:', bubbleId)
    handleModeChange(bubbleId, 'editing')
  }, [handleModeChange])

  // ✅ GESTION DES ÉVÉNEMENTS DE COUCHE - DÉSÉLECTION COMME LES PANELS
  const handleLayerClick = (e: React.MouseEvent) => {
    // Si on clique sur la couche (pas sur une bulle), désélectionner tout
    if (e.target === e.currentTarget) {
      console.log('🧹 Clic sur couche vide - désélection')
      selectElement(null)
      setEditingBubbleId(null)
    }
  }

  // ✅ SOLUTION ALTERNATIVE : Calculer les coordonnées directement par rapport à la couche
  const getLayerRelativeCoordinates = useCallback((canvasEvent: MouseEvent) => {
    if (!layerRef.current) return { x: 0, y: 0 }

    const layerRect = layerRef.current.getBoundingClientRect()
    const x = canvasEvent.clientX - layerRect.left
    const y = canvasEvent.clientY - layerRect.top

    console.log('🔍 getLayerRelativeCoordinates:', {
      clientX: canvasEvent.clientX,
      clientY: canvasEvent.clientY,
      layerRect: { left: layerRect.left, top: layerRect.top },
      result: { x, y }
    })

    return { x, y }
  }, [])

  // ✅ GESTION DE LA MANIPULATION
  const handleStartManipulation = (
    element: DialogueElement, 
    handleType: string, 
    globalX: number, 
    globalY: number
  ) => {
    console.log('🎯 TipTapBubbleLayer: Début manipulation', {
      elementId: element.id,
      handleType,
      position: { globalX, globalY }
    })
    
    // Passer en mode manipulation
    handleModeChange(element.id, 'manipulating')
  }

  // ✅ STYLES DE LA COUCHE
  const layerStyle = useMemo(() => ({
    position: 'absolute' as const,
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    pointerEvents: 'auto' as const, // ACTIVER les événements pour la désélection
    zIndex: 1000, // Z-index fixe pour la couche des bulles TipTap
    overflow: 'hidden'
  }), [])

  // ✅ SYNCHRONISATION AVEC LE VIEWPORT
  useEffect(() => {
    if (!layerRef.current) return

    const layer = layerRef.current
    // ✅ CORRECTION : Ne pas appliquer de transformation CSS supplémentaire
    // Les bulles sont déjà positionnées en coordonnées absolues
    layer.style.transform = 'none'
    layer.style.transformOrigin = '0 0'

    console.log('🔄 TipTapBubbleLayer: Synchronisation viewport', {
      transform: canvasTransform,
      bubblesCount: bubbles.length
    })
  }, [canvasTransform, bubbles.length])

  return (
    <div
      ref={layerRef}
      className={`tiptap-bubble-layer ${className}`}
      style={layerStyle}
      onClick={handleLayerClick}
    >
      {bubbles.map(bubble => {
        const isSelected = selectedElementIds.includes(bubble.id)
        const mode = getBubbleMode(bubble.id)

        return (
          <TipTapBubble
            key={bubble.id}
            element={bubble}
            isSelected={isSelected}
            mode={mode}
            onSelect={selectElement}
            onModeChange={handleModeChange}
            onUpdate={updateElement}
            onDoubleClick={handleBubbleDoubleClick}
          />
        )
      })}
    </div>
  )
}
