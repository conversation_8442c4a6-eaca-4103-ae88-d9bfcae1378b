'use client'

// BubbleQueue - Système de queue graphique pour les speech bubbles
// Rendu SVG pour des queues précises et personnalisables

import React, { useMemo } from 'react'
import { DialogueElement, BubbleType } from '../types/assembly.types'

interface BubbleQueueProps {
  element: DialogueElement
  className?: string
}

/**
 * Composant pour rendre la queue d'une bulle de dialogue
 * Utilise SVG pour un rendu précis et scalable
 */
export default function BubbleQueue({ element, className = '' }: BubbleQueueProps) {
  
  // ✅ GÉNÉRATION DE LA QUEUE SELON LE TYPE ET LA POSITION
  const queuePath = useMemo(() => {
    const { tailPosition, tailLength, tailAngleDegrees, type } = element.dialogueStyle
    const { width, height } = element.transform
    
    // Calculer la position de base de la queue
    const basePosition = getQueueBasePosition(tailPosition, width, height)
    
    // Générer le chemin SVG selon le type de bulle
    switch (type) {
      case 'speech':
        return generateSpeechQueue(basePosition, tailLength, tailAngleDegrees)
      case 'thought':
        return generateThoughtQueue(basePosition, tailLength)
      case 'shout':
        return generateShoutQueue(basePosition, tailLength, tailAngleDegrees)
      case 'whisper':
        return generateWhisperQueue(basePosition, tailLength, tailAngleDegrees)
      case 'explosion':
        return null // Pas de queue pour les explosions
      default:
        return generateSpeechQueue(basePosition, tailLength, tailAngleDegrees)
    }
  }, [element.dialogueStyle, element.transform])

  // ✅ STYLES DE LA QUEUE SELON LE TYPE
  const queueStyle = useMemo(() => {
    const { type, outlineColor, outlineWidth, backgroundColor } = element.dialogueStyle
    
    const baseStyle = {
      fill: typeof backgroundColor === 'string' 
        ? backgroundColor 
        : `#${backgroundColor.toString(16).padStart(6, '0')}`,
      stroke: typeof outlineColor === 'string' 
        ? outlineColor 
        : `#${outlineColor.toString(16).padStart(6, '0')}`,
      strokeWidth: outlineWidth
    }

    // Styles spécifiques selon le type
    switch (type) {
      case 'thought':
        return {
          ...baseStyle,
          strokeDasharray: '5,5'
        }
      case 'whisper':
        return {
          ...baseStyle,
          strokeDasharray: '3,3',
          opacity: 0.8
        }
      case 'shout':
        return {
          ...baseStyle,
          strokeWidth: outlineWidth * 1.5
        }
      default:
        return baseStyle
    }
  }, [element.dialogueStyle])

  // Ne pas rendre si pas de queue ou type explosion
  if (!queuePath || element.dialogueStyle.type === 'explosion') {
    return null
  }

  return (
    <svg
      className={`bubble-queue absolute ${className}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        overflow: 'visible'
      }}
    >
      <path
        d={queuePath}
        style={queueStyle}
      />
    </svg>
  )
}

// ✅ FONCTIONS UTILITAIRES POUR GÉNÉRER LES QUEUES

/**
 * Calcule la position de base de la queue selon la position configurée
 */
function getQueueBasePosition(
  tailPosition: string, 
  width: number, 
  height: number
): { x: number; y: number } {
  switch (tailPosition) {
    case 'top-left':
      return { x: width * 0.2, y: 0 }
    case 'top-right':
      return { x: width * 0.8, y: 0 }
    case 'bottom-left':
      return { x: width * 0.2, y: height }
    case 'bottom-right':
      return { x: width * 0.8, y: height }
    default:
      return { x: width * 0.2, y: height }
  }
}

/**
 * Génère une queue de bulle de dialogue classique (triangulaire)
 */
function generateSpeechQueue(
  basePosition: { x: number; y: number },
  length: number,
  angle: number
): string {
  const { x, y } = basePosition
  const angleRad = (angle * Math.PI) / 180
  
  // Point de la pointe de la queue
  const tipX = x + Math.cos(angleRad) * length
  const tipY = y + Math.sin(angleRad) * length
  
  // Points de la base (largeur de 20px)
  const baseWidth = 20
  const perpAngle = angleRad + Math.PI / 2
  const base1X = x + Math.cos(perpAngle) * (baseWidth / 2)
  const base1Y = y + Math.sin(perpAngle) * (baseWidth / 2)
  const base2X = x - Math.cos(perpAngle) * (baseWidth / 2)
  const base2Y = y - Math.sin(perpAngle) * (baseWidth / 2)
  
  return `M ${base1X} ${base1Y} L ${tipX} ${tipY} L ${base2X} ${base2Y} Z`
}

/**
 * Génère une queue de bulle de pensée (petites bulles)
 */
function generateThoughtQueue(
  basePosition: { x: number; y: number },
  length: number
): string {
  const { x, y } = basePosition
  
  // Créer plusieurs petites bulles décroissantes
  const bubbles = []
  const numBubbles = 3
  
  for (let i = 0; i < numBubbles; i++) {
    const distance = (i + 1) * (length / numBubbles)
    const radius = 8 - i * 2 // Bulles décroissantes
    const bubbleX = x + distance * 0.3
    const bubbleY = y + distance
    
    bubbles.push(`M ${bubbleX + radius} ${bubbleY} A ${radius} ${radius} 0 1 1 ${bubbleX - radius} ${bubbleY} A ${radius} ${radius} 0 1 1 ${bubbleX + radius} ${bubbleY}`)
  }
  
  return bubbles.join(' ')
}

/**
 * Génère une queue de bulle de cri (dentelée)
 */
function generateShoutQueue(
  basePosition: { x: number; y: number },
  length: number,
  angle: number
): string {
  const { x, y } = basePosition
  const angleRad = (angle * Math.PI) / 180
  
  // Queue dentelée avec plusieurs pointes
  const points = []
  const numSpikes = 5
  const baseWidth = 25
  
  for (let i = 0; i <= numSpikes; i++) {
    const progress = i / numSpikes
    const currentLength = length * progress
    const spikeOffset = (i % 2 === 0) ? 0 : 8 // Alternance pour effet dentelé
    
    const pointX = x + Math.cos(angleRad) * currentLength + Math.cos(angleRad + Math.PI / 2) * spikeOffset
    const pointY = y + Math.sin(angleRad) * currentLength + Math.sin(angleRad + Math.PI / 2) * spikeOffset
    
    points.push(`${i === 0 ? 'M' : 'L'} ${pointX} ${pointY}`)
  }
  
  return points.join(' ')
}

/**
 * Génère une queue de bulle de chuchotement (fine et ondulée)
 */
function generateWhisperQueue(
  basePosition: { x: number; y: number },
  length: number,
  angle: number
): string {
  const { x, y } = basePosition
  const angleRad = (angle * Math.PI) / 180
  
  // Queue fine et légèrement ondulée
  const tipX = x + Math.cos(angleRad) * length
  const tipY = y + Math.sin(angleRad) * length
  
  // Base plus fine que la queue normale
  const baseWidth = 12
  const perpAngle = angleRad + Math.PI / 2
  const base1X = x + Math.cos(perpAngle) * (baseWidth / 2)
  const base1Y = y + Math.sin(perpAngle) * (baseWidth / 2)
  const base2X = x - Math.cos(perpAngle) * (baseWidth / 2)
  const base2Y = y - Math.sin(perpAngle) * (baseWidth / 2)
  
  // Point de contrôle pour une légère courbe
  const midX = (x + tipX) / 2 + Math.cos(perpAngle) * 5
  const midY = (y + tipY) / 2 + Math.sin(perpAngle) * 5
  
  return `M ${base1X} ${base1Y} Q ${midX} ${midY} ${tipX} ${tipY} Q ${midX - 10} ${midY} ${base2X} ${base2Y} Z`
}
